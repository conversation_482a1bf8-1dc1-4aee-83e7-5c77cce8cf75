#!/bin/sh

echo "=== Next.js Health Check ==="
echo "Time: $(date)"
echo "NODE_ENV: $NODE_ENV"
echo "PORT: $PORT"
echo "BUILD_ENV: $BUILD_ENV"
echo ""

echo "=== Process Check ==="
ps aux | grep -E "(next|node)" | grep -v grep
echo ""

echo "=== Port Check ==="
netstat -tlnp | grep :80 || echo "Port 80 not listening"
echo ""

echo "=== Application Check ==="
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:80/web/ || echo "Failed to connect to localhost:80/web/"
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:80/ || echo "Failed to connect to localhost:80/"
echo ""

echo "=== File System Check ==="
ls -la /app/.next/ | head -10
echo ""

echo "=== Logs Check ==="
echo "Recent logs:"
tail -20 /proc/1/fd/1 2>/dev/null || echo "No logs available"
