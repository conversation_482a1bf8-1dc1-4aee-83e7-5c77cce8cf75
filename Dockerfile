# 构建阶段
FROM *************:5000/third/node:20-alpine as build-stage
WORKDIR /app
COPY ./ .
ARG BUILD_ENV
RUN if [ -z "$BUILD_ENV" ]; then echo "BUILD_ENV is not set"; exit 1; fi
RUN npm config set registry https://mirrors.huaweicloud.com/repository/npm/
RUN npm install -g pnpm@8.5.1
# 清理可能存在的 node_modules
RUN rm -rf node_modules
RUN pnpm install
# 根据环境变量执行不同的构建命令
RUN  pnpm run build

# 生产阶段
FROM *************:5000/third/node:20-alpine
ARG BUILD_ENV
WORKDIR /app
COPY --from=build-stage /app ./
ENV NODE_ENV="$BUILD_ENV"
ENV PORT=80
ENV HOSTNAME="0.0.0.0"
EXPOSE 80
# 使用自定义服务器或标准 Next.js 服务器
CMD ["node", "server.js"]
