# 构建阶段
FROM 192.168.0.249:5000/third/node:16.14.2 as build-stage
WORKDIR /app
COPY ./ .
ARG BUILD_ENV
RUN if [ -z "$BUILD_ENV" ]; then echo "BUILD_ENV is not set"; exit 1; fi
RUN npm config set registry https://mirrors.huaweicloud.com/repository/npm/
RUN npm install -g pnpm@8.5.1
RUN pnpm install
# 根据环境变量执行不同的构建命令
RUN  pnpm run build

# 生产阶段 - 使用 Nginx 提供静态文件服务
FROM 192.168.0.249:5000/third/node:16.14.2
WORKDIR /app
COPY --from=builder /app ./
ENV NODE_ENV  "$BUILD_ENV"
EXPOSE 80
CMD ["npx", "next", "start"]
