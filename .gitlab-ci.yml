# GitLab CI/CD 配置文件 - Next.js 问卷系统
stages:
  - deploy
  - deploy-prod
  - clean

variables:
  masterVersion: v1.0.0

# 开发环境部署
docker_deploy_questionnaire_dev:
  only:
    - dev
  stage: deploy
  tags:
    - ci-runner
  script:
    - docker build --build-arg BUILD_ENV="test" -t questionnaire-web .
    - if [ $(docker ps -aq --filter name=^/questionnaire-web$) ]; then docker rm -f questionnaire-web;fi
    - docker run --name questionnaire-web -d -p 7201:80 questionnaire-web
  when: manual

# 生产环境部署
docker_deploy_questionnaire_prod:
  only:
    - master
  stage: deploy-prod
  tags:
    - ci-runner
  script:
    - docker build --build-arg BUILD_ENV="prod" -t questionnaire-web-prod .
    - docker tag questionnaire-web-prod *************:5000/slan-bot/questionnaire-web:$masterVersion
    - docker push *************:5000/slan-bot/questionnaire-web:$masterVersion
  when: manual

# 清理未使用的 Docker 镜像
clean_docker_images:
  stage: clean
  tags:
    - ci-runner
  script:
    - docker image prune -f
    - docker container prune -f
  when: manual
  only:
    - master
    - dev
