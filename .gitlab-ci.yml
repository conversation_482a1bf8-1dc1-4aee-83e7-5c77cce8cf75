# GitLab CI/CD 配置文件 - Next.js 项目部署
image: node:18-alpine

# 定义缓存策略
cache:
  paths:
    - node_modules/
    - .pnpm-store/

# 定义阶段
stages:
  - install
  - lint
  - build
  - deploy

# 全局变量
variables:
  PNPM_CACHE_FOLDER: .pnpm-store
  NODE_ENV: production

# 安装依赖阶段
install_dependencies:
  stage: install
  before_script:
    - npm install -g pnpm
    - pnpm config set store-dir $PNPM_CACHE_FOLDER
  script:
    - pnpm install --frozen-lockfile
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  only:
    - main
    - develop
    - merge_requests

# 代码检查阶段
lint_code:
  stage: lint
  dependencies:
    - install_dependencies
  before_script:
    - npm install -g pnpm
  script:
    - pnpm lint
  only:
    - main
    - develop
    - merge_requests

# 构建阶段
build_project:
  stage: build
  dependencies:
    - install_dependencies
  before_script:
    - npm install -g pnpm
  script:
    - pnpm build
  artifacts:
    paths:
      - .next/
      - out/
    expire_in: 1 day
  only:
    - main
    - develop

# 部署到开发环境
deploy_development:
  stage: deploy
  dependencies:
    - build_project
  before_script:
    - 'which ssh-agent || ( apk add --update openssh )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $DEV_SERVER_HOST >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "部署到开发环境..."
    - |
      ssh $DEV_SERVER_USER@$DEV_SERVER_HOST << 'EOF'
        cd /var/www/questionnaire-dev
        git pull origin develop
        npm install -g pnpm
        pnpm install --frozen-lockfile
        pnpm build
        pm2 restart questionnaire-dev || pm2 start ecosystem.config.js --env development
      EOF
  environment:
    name: development
    url: https://dev-questionnaire.yourdomain.com
  only:
    - develop

# 部署到生产环境
deploy_production:
  stage: deploy
  dependencies:
    - build_project
  before_script:
    - 'which ssh-agent || ( apk add --update openssh )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $PROD_SERVER_HOST >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "部署到生产环境..."
    - |
      ssh $PROD_SERVER_USER@$PROD_SERVER_HOST << 'EOF'
        cd /var/www/questionnaire-prod
        git pull origin main
        npm install -g pnpm
        pnpm install --frozen-lockfile
        pnpm build
        pm2 restart questionnaire-prod || pm2 start ecosystem.config.js --env production
      EOF
  environment:
    name: production
    url: https://questionnaire.yourdomain.com
  when: manual
  only:
    - main

# Docker 部署方案（可选）
deploy_docker:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  dependencies:
    - build_project
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:latest
  only:
    - main
  when: manual
