# 构建阶段
FROM *************:5000/third/node:20-alpine as build-stage
WORKDIR /app
COPY ./ .
ARG BUILD_ENV
RUN if [ -z "$BUILD_ENV" ]; then echo "BUILD_ENV is not set"; exit 1; fi
RUN npm config set registry https://mirrors.huaweicloud.com/repository/npm/
RUN npm install -g pnpm@8.5.1
# 清理可能存在的 node_modules
RUN rm -rf node_modules
RUN pnpm install
# 根据环境变量执行不同的构建命令
RUN echo "Building with BUILD_ENV: $BUILD_ENV"
RUN pnpm run build
RUN echo "Build completed, checking output:"
RUN ls -la .next/

# 生产阶段
FROM *************:5000/third/node:20-alpine
ARG BUILD_ENV
WORKDIR /app

# 安装调试工具
RUN apk add --no-cache curl netstat-nat

COPY --from=build-stage /app ./
COPY healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

ENV NODE_ENV="$BUILD_ENV"
ENV PORT=80
ENV HOSTNAME="0.0.0.0"
ENV DEBUG="*"

EXPOSE 80

# 启动脚本
RUN echo '#!/bin/sh' > /start.sh && \
    echo 'echo "=== Container Starting ==="' >> /start.sh && \
    echo 'echo "NODE_ENV: $NODE_ENV"' >> /start.sh && \
    echo 'echo "BUILD_ENV: $BUILD_ENV"' >> /start.sh && \
    echo 'echo "PORT: $PORT"' >> /start.sh && \
    echo 'echo "Current directory: $(pwd)"' >> /start.sh && \
    echo 'echo "Files in .next:"' >> /start.sh && \
    echo 'ls -la .next/ | head -10' >> /start.sh && \
    echo 'echo "Starting Next.js..."' >> /start.sh && \
    echo 'exec npx next start -p 80 -H 0.0.0.0' >> /start.sh && \
    chmod +x /start.sh

CMD ["/start.sh"]
