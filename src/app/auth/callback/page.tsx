'use client';

import { useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useUserStore } from '@/store/user';

function AuthCallback() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { loginWithCode, isProcessing, user, isInitialized } = useUserStore();

  useEffect(() => {
    // 等待初始化完成
    if (!isInitialized) return;

    // 如果用户已经登录，直接重定向到首页
    if (user) {
      console.log('User already logged in, redirecting to home...');
      router.push('/');
      return;
    }

    const code = searchParams.get('code');
    const error = searchParams.get('error');

    if (error) {
      // Handle error from auth server
      console.error(`OAuth Error: ${error}`);
      alert(`认证失败: ${error}`);
      router.push('/login');
      return;
    }

    if (code) {
      console.log('Processing code:', code);
      loginWithCode(code)
        .then(() => {
          console.log('Login successful, redirecting to home...');
          router.push('/'); // Redirect to home page
        })
        .catch((err) => {
          console.error('Login failed:', err);
          // router.push('/login');
        });
    } else {
      // 没有 code 也没有错误，可能是直接访问，等待一段时间后重定向
      console.log('No code found, waiting before redirect...');
      const timer = setTimeout(() => {
        router.push('/login');
      }, 2000); // 等待 2 秒后重定向

      return () => clearTimeout(timer);
    }
  }, [searchParams, loginWithCode, router, user, isInitialized]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <p className="text-lg">正在验证您的身份，请稍候...</p>
        {isProcessing && <p className="text-sm text-muted-foreground mt-2">处理中，请勿刷新页面...</p>}
        {user && <p className="text-sm text-green-500 dark:text-green-400 mt-2">检测到已登录，正在跳转...</p>}
        {!searchParams.get('code') && !searchParams.get('error') && (
          <p className="text-sm text-yellow-500 dark:text-yellow-400 mt-2">未检测到授权码，即将跳转到登录页面...</p>
        )}
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AuthCallback />
    </Suspense>
  );
} 