import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { UserProvider } from "@/components/providers/UserProvider";
import { ClientOnly } from "@/components/providers/ClientOnly";
import { ThemeProvider } from "next-themes";
import { AITaskMonitor, AITaskIndicator } from "@/components/business/ai-task-monitor";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "智能问卷系统",
  description: "slan-智能问卷系统",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <ClientOnly>
            <UserProvider>
              {children}
              <Toaster />
              <AITaskMonitor />
              <AITaskIndicator />
            </UserProvider>
          </ClientOnly>
        </ThemeProvider>
      </body>
    </html>
  );
}
