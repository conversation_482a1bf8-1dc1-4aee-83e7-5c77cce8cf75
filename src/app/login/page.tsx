'use client';

import { useEffect, useState } from 'react';
import { useUserStore } from '@/store/user';
import { useRouter } from 'next/navigation';

const LoginPage = () => {
  const [redirectUri, setRedirectUri] = useState('');
  const [hasRedirected, setHasRedirected] = useState(false);
  const { user, isInitialized } = useUserStore();
  const router = useRouter();

  useEffect(() => {
    // 确保 window 已定义（仅在客户端运行）
    if (typeof window !== 'undefined') {
      setRedirectUri(
        `${window.location.protocol}//${window.location.host}/auth/callback`
      );
    }
  }, []);

  useEffect(() => {
    // 等待初始化完成
    if (!isInitialized) return;

    // 如果用户已经登录，直接重定向到首页
    if (user) {
      console.log('User already logged in, redirecting to home...');
      router.push('/');
      return;
    }

    // 如果还没有重定向过，且有 redirectUri，则自动重定向
    if (!hasRedirected && redirectUri) {
      console.log('Auto redirecting to OAuth authorization...');
      setHasRedirected(true);
      const backendAuthUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/oauth/authorize?redirectUri=${encodeURIComponent(
        redirectUri
      )}`;
      window.location.href = backendAuthUrl;
    }
  }, [user, isInitialized, router, redirectUri, hasRedirected]);

  // 如果还在初始化，显示加载状态
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">正在初始化...</p>
        </div>
      </div>
    );
  }

  // 如果用户已登录，显示重定向状态
  if (user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">检测到已登录，正在跳转...</p>
        </div>
      </div>
    );
  }

  // 如果正在重定向，显示重定向状态
  if (hasRedirected) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">正在重定向到登录页面...</p>
        </div>
      </div>
    );
  }

  // 如果还没有开始重定向，显示登录页面
  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="p-8 bg-card rounded-lg shadow-md border border-border">
        <h1 className="text-2xl font-bold mb-6 text-center text-card-foreground">登录</h1>
        <p className="text-center mb-4 text-muted-foreground">正在准备登录...</p>
      </div>
    </div>
  );
};

export default LoginPage; 