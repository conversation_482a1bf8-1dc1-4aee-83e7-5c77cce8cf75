"use client";

import { useEffect, useRef, useState, createRef } from "react";
import { useParams } from "next/navigation";
import { questionnaireApi } from "@/lib/api/questionnaire";
import {
  QuestionnaireResponseDTO,
  QuestionResponseDTO,
  QuestionType,
  RadioQuestionProps,
  CheckboxQuestionProps,
  SelectQuestionProps,
} from "@/types/questionnaire";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArrowLeft, Pencil } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const BG_COLOR = "#ede7f6";
const PRIMARY = "#8B6BE8";
const PHONE_W = 393;
const PHONE_H = 852;

export default function QuestionnairePreview() {
  const params = useParams();
  const [questionnaire, setQuestionnaire] =
    useState<QuestionnaireResponseDTO | null>(null);
  const [current, setCurrent] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [isAnimating, setIsAnimating] = useState(false);
  const [showAnswerBubble, setShowAnswerBubble] = useState<
    Record<number, boolean>
  >({});
  const chatRef = useRef<HTMLDivElement>(null);
  const questionRefs = useRef<React.RefObject<HTMLDivElement>[]>([]);
  const [chatAreaHeight, setChatAreaHeight] = useState(0);

  const questions =
    questionnaire?.questions?.filter((q) => q.crud !== 3) || [];

  if (questionRefs.current.length !== questions.length) {
    questionRefs.current = Array(questions.length)
      .fill(null)
      .map((_, i) => questionRefs.current[i] || createRef());
  }

  useEffect(() => {
    questionnaireApi.getById(params.id as string).then(setQuestionnaire);
  }, [params.id]);

  useEffect(() => {
    const setHeight = () => {
      if (chatRef.current) {
        // Use a timeout to ensure layout is calculated
        setTimeout(() => {
          setChatAreaHeight(chatRef.current?.clientHeight || 0);
        }, 50);
      }
    };

    if (questionnaire) {
      setHeight();
    }

    window.addEventListener("resize", setHeight);
    return () => window.removeEventListener("resize", setHeight);
  }, [questionnaire]);

  useEffect(() => {
    if (questionRefs.current[current] && chatAreaHeight > 0) {
      setTimeout(() => {
        questionRefs.current[current].current?.scrollIntoView({
          behavior: "smooth",
          block: "end",
        });
      }, 100);
    }
  }, [current, chatAreaHeight]);

  const total = questions.length;

  const handleAnswer = async (val: any) => {
    if (isAnimating) return;
    setIsAnimating(true);

    const currentQuestion = questions[current];
    const questionCode = currentQuestion.props.code || "";
    setAnswers((a) => ({ ...a, [questionCode]: val }));

    // This triggers the layout animation from input to answer bubble
    setShowAnswerBubble((prev) => ({ ...prev, [current]: true }));

    // Wait for the animation to finish and for the user to see the result
    await new Promise((res) => setTimeout(res, 700));

    if (current < total - 1) {
      setCurrent((c) => c + 1);
    } else {
      setCurrent((c) => c + 1);
    }

    setIsAnimating(false);
  };

  const handleEdit = (idx: number) => {
    if (isAnimating) return;
    setCurrent(idx);
    setShowAnswerBubble((prev) => {
      const newMap = { ...prev };
      delete newMap[idx];
      return newMap;
    });
  };

  if (!questionnaire)
    return (
      <div className="flex items-center justify-center h-screen">加载中...</div>
    );

  if (current >= total) {
    return (
      <div
        className="w-full h-screen flex items-center justify-center"
        style={{ background: BG_COLOR }}
      >
        <div
          className="relative bg-white rounded-[2.5rem] shadow-2xl border-4 border-gray-800 flex flex-col pb-5"
          style={{ width: PHONE_W, height: PHONE_H }}
        >
          <Header />
          <ProgressBar progress={100} />
          <div className="flex-1 flex flex-col justify-center items-center text-center p-8">
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", delay: 0.2 }}
              className="w-24 h-24 bg-gradient-to-br from-[#bcaaff] to-[#8B6BE8] rounded-full flex items-center justify-center shadow-lg mb-6"
            >
              <span className="text-5xl">🎉</span>
            </motion.div>
            <motion.h1
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="text-gray-800 text-2xl font-bold"
            >
              问卷已完成！
            </motion.h1>
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="text-gray-500 mt-2"
            >
              感谢您的参与。
            </motion.p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="w-full h-screen flex items-center justify-center"
      style={{ background: BG_COLOR }}
    >
      <div
        className="relative bg-[#f7f3ff] rounded-[2.5rem] shadow-2xl border-4 border-gray-800 flex flex-col pb-5"
        style={{ width: PHONE_W, height: PHONE_H }}
      >
        <Header />
        <ProgressBar progress={((current) / total) * 100} />
        <div
          ref={chatRef}
          className="flex-1 flex flex-col w-full px-4 overflow-y-auto custom-scrollbar"
        >
          {questions.map((q, idx) => (
            <div
              key={q.id}
              ref={questionRefs.current[idx]}
              className="flex flex-col pt-1"
              style={
                idx === current && chatAreaHeight > 0
                  ? { minHeight: chatAreaHeight }
                  : {}
              }
            >
              <ChatItem
                q={q}
                idx={idx}
                current={current}
                answers={answers}
                showAnswerBubble={showAnswerBubble[idx]}
                isAnimating={isAnimating}
                onAnswer={handleAnswer}
                onEdit={handleEdit}
              />
            </div>
          ))}
        </div>
        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: #d1c4e9;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #8b6be8;
            border-radius: 4px;
          }
          .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #8b6be8 #d1c4e9;
          }
        `}</style>
      </div>
    </div>
  );
}

function ChatItem({
  q,
  idx,
  current,
  answers,
  showAnswerBubble,
  isAnimating,
  onAnswer,
  onEdit,
}: any) {
  const isCurrent = current === idx;
  const questionCode = q.props.code || "";
  const answerValue = answers[questionCode];
  const layoutId = `bubble-${q.id}`;

  return (
    <div
      className={`w-full flex flex-col mb-6 pt-4 ${
        isCurrent ? "flex-grow" : ""
      }`}
    >
      {/* Robot Question Bubble */}
      <AnimatePresence>
        {idx <= current && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.4, delay: isCurrent ? 0.6 : 0 }}
            className="flex w-full mb-2"
          >
            <div className="flex items-start gap-2">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#bcaaff] to-[#8B6BE8] flex items-center justify-center shadow-lg text-2xl flex-shrink-0">
                🤖
              </div>
              <div className="bg-white rounded-2xl px-4 py-3 shadow text-left">
                <div className="font-medium text-base">{q.props.title}</div>
                {q.props.description && (
                  <div className="text-xs text-gray-500 mt-1">
                    {q.props.description}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* User Answer Bubble & Input Area */}
      <div className="flex flex-col items-end justify-start w-full">
        <AnimatePresence initial={false}>
          {showAnswerBubble ? (
            <motion.div
              layoutId={layoutId}
              key="answer"
              transition={{ type: "spring", stiffness: 400, damping: 30 }}
              className="flex items-center gap-2"
            >
              <div className="bg-green-400 text-white rounded-2xl px-4 py-2 shadow max-w-[220px] text-right relative group">
                {renderAnswer(q, answerValue)}
                <button
                  className="absolute -left-6 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => onEdit(idx)}
                >
                  <Pencil className="w-4 h-4 text-gray-600" />
                </button>
              </div>
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-2xl flex-shrink-0">
                👤
              </div>
            </motion.div>
          ) : isCurrent ? (
            <motion.div
              layoutId={layoutId}
              key="input"
              initial={{ opacity: 0, y: 20 }}
              animate={{
                opacity: 1,
                y: 0,
                transition: { duration: 0.4, delay: 0.8 },
              }}
              exit={{
                opacity: 0,
                height: 0,
                paddingTop: 0,
                paddingBottom: 0,
                marginTop: 0,
                transition: { duration: 0.4, ease: "easeInOut" },
              }}
              className="w-full bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg mt-2 self-end"
            >
              <InputArea q={q} onAnswer={onAnswer} isAnimating={isAnimating} />
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>
    </div>
  );
}

function InputArea({ q, onAnswer, isAnimating }: any) {
  const [value, setValue] = useState<any>(null);

  const handleSubmit = () => {
    if (value !== null && !isAnimating) {
      onAnswer(value);
    }
  };

  const renderInput = () => {
    switch (q.type) {
      case QuestionType.Radio:
        return (
          <div className="w-full">
            {(q.props as any).options.map((opt: any) => (
              <OptionBtn
                key={opt.value}
                selected={value === opt.value}
                onClick={() => setValue(opt.value)}
              >
                {opt.label}
              </OptionBtn>
            ))}
          </div>
        );
      case QuestionType.Checkbox:
        const checkboxValue = Array.isArray(value) ? value : [];
        return (
          <div className="w-full">
            {(q.props as any).options.map((opt: any) => (
              <OptionBtn
                key={opt.value}
                selected={checkboxValue.includes(opt.value)}
                onClick={() => {
                  const newArray = checkboxValue.includes(opt.value)
                    ? checkboxValue.filter((v: any) => v !== opt.value)
                    : [...checkboxValue, opt.value];
                  setValue(newArray);
                }}
              >
                {opt.label}
              </OptionBtn>
            ))}
          </div>
        );
      case QuestionType.Input:
      case QuestionType.Text:
        return (
          <form
            className="w-full flex flex-col gap-4"
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit();
            }}
          >
            <Input
              value={value || ""}
              onChange={(e) => setValue(e.target.value)}
              className="rounded-2xl border-2 border-[${PRIMARY}] py-3 px-4 text-lg h-14"
              style={{ borderColor: PRIMARY }}
              placeholder="请输入..."
              disabled={isAnimating}
            />
          </form>
        );
      case QuestionType.Select: {
        const props = q.props as SelectQuestionProps;
        return (
          <Select onValueChange={setValue} value={value || ""}>
            <SelectTrigger
              className="w-full rounded-2xl border-2 border-[${PRIMARY}] py-3 px-4 text-lg h-14"
              style={{ borderColor: PRIMARY }}
            >
              <SelectValue placeholder="请选择..." />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {props.options.map((opt: any) => (
                  <SelectItem key={opt.value} value={opt.value}>
                    {opt.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        );
      }
      default:
        return (
          <div className="text-red-500">
            Unsupported question type: {q.type}
          </div>
        );
    }
  };

  return (
    <div className="w-full">
      {renderInput()}
      <Button
        className="w-full mt-2 bg-[${PRIMARY}] text-white rounded-2xl py-3 text-lg h-14"
        style={{ background: PRIMARY }}
        onClick={handleSubmit}
        disabled={
          isAnimating ||
          value === null ||
          (Array.isArray(value) && value.length === 0)
        }
      >
        {isAnimating ? "..." : "继续"}
      </Button>
    </div>
  );
}

function OptionBtn({ children, selected, ...rest }: any) {
  return (
    <button
      className={`w-full py-4 mb-3 rounded-2xl border-2 text-lg font-medium transition-all h-14 active:scale-95
        ${
          selected
            ? `border-[${PRIMARY}] bg-[${PRIMARY}] text-white shadow-md`
            : `border-gray-300 text-[${PRIMARY}] bg-white hover:bg-purple-50`
        }
      `}
      style={{
        borderColor: selected ? PRIMARY : "#d1d5db",
        color: selected ? "#fff" : PRIMARY,
        background: selected ? PRIMARY : "#fff",
      }}
      {...rest}
    >
      {children}
    </button>
  );
}

function renderAnswer(q: QuestionResponseDTO, val: any) {
  if (!val) return "未回答";

  switch (q.type) {
    case QuestionType.Radio:
    case QuestionType.Select: {
      const props = q.props as RadioQuestionProps | SelectQuestionProps;
      return props.options.find((o) => o.value === val)?.label || val;
    }
    case QuestionType.Checkbox: {
      const props = q.props as CheckboxQuestionProps;
      if (!Array.isArray(val)) return val;
      return val
        .map((v) => props.options.find((o) => o.value === v)?.label || v)
        .join("、 ");
    }
    default:
      return val;
  }
}

function ProgressBar({ progress }: { progress: number }) {
  return (
    <div className="w-full bg-gray-200/50 h-2 mt-2">
      <motion.div
        className="h-2 bg-[#8B6BE8]"
        style={{ width: `${progress}%` }}
        initial={{ width: 0 }}
        animate={{ width: `${progress}%` }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
      />
    </div>
  );
}

function Header() {
  return (
    <div className="flex items-center justify-between px-4 pt-4 pb-2 bg-white/50 backdrop-blur-sm rounded-t-[2.5rem]">
      <button className="p-2" onClick={() => history.back()}>
        <ArrowLeft className="w-6 h-6 text-gray-700" />
      </button>
      <div className="text-gray-800 text-lg font-bold flex-1 text-center">
        智能评估
      </div>
      <div className="w-8" />
    </div>
  );
}

 