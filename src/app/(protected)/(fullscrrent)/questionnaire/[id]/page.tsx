"use client";

import { use<PERSON>ara<PERSON> } from "next/navigation";
import { useEffect, useMemo, useState, useRef } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { questionnaire<PERSON><PERSON> } from "@/lib/api/questionnaire";
import {
  QuestionType,
  QuestionProps,
  QuestionResponseDTO,
} from "@/types/questionnaire";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save, Undo, Redo, Eye } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useQuestionEditStore } from "@/store/question-edit";
import { QuestionTypeRegistry } from "@/config/question-type-registry";
import {
  QuestionPropsEditor,
  PropertyPanel,
} from "@/components/business/question-props-editor";
import { CategorizedQuestionPanel } from "@/components/business/CategorizedQuestionPanel";
import { DroppableQuestionList } from "@/components/business/DroppableQuestionList";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";

// 禁用SSR，确保组件只在客户端渲染
export const dynamic = "force-dynamic";

export default function QuestionnaireEditPage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const {
    questionnaire,
    setQuestionnaire,
    addQuestion,
    updateQuestion,
    removeQuestion,
    moveQuestion,
    undo,
    redo,
    history,
    redoStack,
    resetHistory,
    setFullState,
    updateQuestionnaireProps,
    updateLogicExpressions,
  } = useQuestionEditStore();
  const questions = questionnaire?.questions ?? [];
  const [saving, setSaving] = useState(false);
  const filterDeleted = useMemo(
    () => questions?.filter((q) => q.crud !== 3),
    [questions]
  );
  const [showDraftDialog, setShowDraftDialog] = useState(false);
  const [draftData, setDraftData] = useState<any>(null);
  const DRAFT_KEY = `questionnaire_draft_${params.id}`;
  const [showLeaveDialog, setShowLeaveDialog] = useState(false);

  // 工具函数：UI index => 原始 questions index
  const getRealIndex = (uiIndex: number) => {
    const id = filterDeleted[uiIndex]?.id;
    return questions.findIndex((q) => q.id === id);
  };

  const { data } = useQuery({
    queryKey: ["questionnaire", params.id],
    queryFn: () => questionnaireApi.getById(params.id as string),
  });

  useEffect(() => {
    if (data) {
      setQuestionnaire(
        data,
        true // 标记为从服务器加载
      );
    }
  }, [data, setQuestionnaire]);

  // 进入页面时检测本地草稿
  useEffect(() => {
    const draft = localStorage.getItem(DRAFT_KEY);
    if (draft) {
      try {
        const parsed = JSON.parse(draft);
        if (
          parsed.questions &&
          Array.isArray(parsed.questions) &&
          parsed.questions.length > 0
        ) {
          setDraftData(parsed);
          setShowDraftDialog(true);
        }
      } catch {}
    }
    // eslint-disable-next-line
  }, []);

  // 定时保存完整store到本地（只有有修改时才保存）
  useEffect(() => {
    const timer = setInterval(() => {
      if (questions && questions.length > 0 && history.length > 0) {
        const draft = {
          questions,
          history,
          redoStack,
        };
        localStorage.setItem(DRAFT_KEY, JSON.stringify(draft));
      }
    }, 30000);
    return () => clearInterval(timer);
  }, [questions, history, redoStack]);

  // 用户选择加载本地草稿
  const handleLoadDraft = () => {
    if (draftData) {
      setFullState(draftData);
    }
    setShowDraftDialog(false);
  };

  // 用户选择不加载
  const handleIgnoreDraft = () => {
    localStorage.removeItem(DRAFT_KEY);
    setShowDraftDialog(false);
  };

  const handleQuestionAdd = (type: QuestionType, uiIndex: number) => {
    const registryItem =
      QuestionTypeRegistry[type as keyof typeof QuestionTypeRegistry];
    if (!registryItem) return;

    const defaultProps = registryItem.props.reduce(
      (acc: Record<string, any>, prop: any) => ({
        ...acc,
        [prop.name]: prop.defaultValue,
      }),
      {} as Record<string, any>
    );

    const newQuestion: QuestionResponseDTO = {
      id: `local_${Date.now()}`,
      type,
      crud: 1,
      sortOrder: uiIndex,
      props: defaultProps as QuestionProps,
    };

    // 关键：插入到原始 questions 的正确位置
    let realIndex: number;
    if (uiIndex >= filterDeleted.length) {
      realIndex = questions.length;
    } else {
      realIndex = getRealIndex(uiIndex);
      if (realIndex === -1) realIndex = questions.length;
    }

    addQuestion(newQuestion, realIndex);
    setSelectedIndex(uiIndex);
  };

  // 选中
  const handleQuestionSelect = (uiIndex: number) => {
    setSelectedIndex(uiIndex);
  };

  // 删除
  const handleQuestionDelete = (uiIndex: number) => {
    const realIndex = getRealIndex(uiIndex);
    if (realIndex === -1) return;
    const question = questions[realIndex];
    if (!question) return;
    removeQuestion(question.id!);
    setSelectedIndex(null);
  };

  // 移动
  const handleQuestionMove = (fromUiIndex: number, toUiIndex: number) => {
    const fromRealIndex = getRealIndex(fromUiIndex);
    const toRealIndex = getRealIndex(toUiIndex);
    if (fromRealIndex === -1 || toRealIndex === -1) return;
    moveQuestion(fromRealIndex, toRealIndex);
    setSelectedIndex(toUiIndex);
  };

  // 编辑
  const handleQuestionEdit = (field: keyof QuestionProps, value: any) => {
    if (selectedIndex === null) return;
    const uiIndex = selectedIndex;
    const realIndex = getRealIndex(uiIndex);
    if (realIndex === -1) return;
    const question = questions[realIndex];
    if (!question) return;
    if (question.crud === 4) {
      question.crud = 2;
    }
    updateQuestion(question.id!, { [field]: value, crud: question.crud });
  };

  const handleSave = async () => {
    if (saving) return;
    setSaving(true);
    // 校验所有题目的 code
    const emptyCodeIndex = questions.findIndex(
      (q) => !q.props.code || String(q.props.code).trim() === ""
    );
    if (emptyCodeIndex !== -1) {
      const q = questions[emptyCodeIndex];
      toast.error(
        `第${emptyCodeIndex + 1}题【${
          q.props.title || "未命名题目"
        }】未填写编码`
      );
      setSaving(false);
      return;
    }
    // 校验 code 是否重复
    const codeMap = new Map<string, number[]>();
    questions.forEach((q, idx) => {
      const code = String(q.props.code).trim();
      if (!codeMap.has(code)) {
        codeMap.set(code, [idx]);
      } else {
        codeMap.get(code)!.push(idx);
      }
    });
    for (const [code, idxs] of codeMap.entries()) {
      if (idxs.length > 1) {
        const firstIdx = idxs[0];
        const q = questions[firstIdx];
        toast.error(
          `第${firstIdx + 1}题【${q.props.title || "未命名题目"}】编码重复`
        );
        setSaving(false);
        return;
      }
    }
    try {
      // 提交前去除 crud=1 的题目的 id
      const submitQuestions = questions.map((q) => {
        if (q.crud === 1) {
          const { id, ...rest } = q;
          return rest;
        }
        return q;
      });
      // console.log("handleSave", submitQuestions);
      await questionnaireApi.updateQuestions({
        questionnaireId: params.id as string,
        questions: submitQuestions,
        logicExpressions: questionnaire?.logicExpressions,
        title: questionnaire?.title || '',
        description: questionnaire?.description || '',
        startTime: questionnaire?.startTime,
        endTime: questionnaire?.endTime,
      });
      toast.success("保存成功");
      // 保存成功后，重置历史和基础版本
      resetHistory();
      queryClient.invalidateQueries({ queryKey: ["questionnaire"] });
      queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
      localStorage.removeItem(DRAFT_KEY);
      setSaving(false);
      router.back(); // 保存成功后回退页面
    } catch (error) {
      toast.error("保存失败");
      setSaving(false);
    }
  };

  // console.log("history", history, redoStack);

  // 返回按钮逻辑
  const handleBack = () => {
    if (history.length === 0) {
      router.back();
    } else {
      setShowLeaveDialog(true);
    }
  };

  // 页面关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (history.length > 0) {
        e.preventDefault();
        e.returnValue = "";
      }
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [history.length]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col bg-background overflow-hidden">
        <header className="h-14 bg-card border-b flex items-center px-4 gap-4 shrink-0">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={undo}
            disabled={history.length === 0} // history为空时表示当前为服务器版本，不能撤销
            title="撤销"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={redo}
            disabled={redoStack.length === 0}
            title="重做"
          >
            <Redo className="h-4 w-4" />
          </Button>
          <h1 className="text-lg font-semibold flex-1 text-card-foreground">
            {questionnaire?.title || "问卷编辑"}
          </h1>
          <Button
            variant="outline"
            onClick={() => router.push(`/questionnaire/preview/${params.id}`)}
            title="预览"
          >
            <Eye className="h-4 w-4 mr-2" />
            预览
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving || history.length === 0}
          >
            {saving ? (
              <span className="mr-2 animate-spin">
                <Save className="h-4 w-4" />
              </span>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {saving ? "保存中..." : "保存"}
          </Button>

          {/* 本地草稿提示弹窗 */}
          <AlertDialog open={showDraftDialog}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>检测到本地草稿</AlertDialogTitle>
                <AlertDialogDescription>
                  是否加载上次未保存的本地草稿？
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={handleIgnoreDraft}>
                  忽略草稿
                </AlertDialogCancel>
                <AlertDialogAction onClick={handleLoadDraft}>
                  加载草稿
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* 离开弹窗 */}
          <AlertDialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>
                  有未保存的更改，确定要离开吗？
                </AlertDialogTitle>
                <AlertDialogDescription>
                  离开后未保存的内容将会丢失。
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setShowLeaveDialog(false)}>
                  取消
                </AlertDialogCancel>
                <AlertDialogAction onClick={() => router.back()}>
                  确定离开
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </header>

        <div className="flex-1 flex gap-6 p-6 overflow-hidden">
          {/* 左侧分类题型面板 */}
          <div className="w-72 overflow-y-auto h-full shrink-0">
            <CategorizedQuestionPanel onQuestionAdd={handleQuestionAdd} />
          </div>

          {/* 中间预览区 */}
          <main className="flex-1 overflow-y-auto min-w-0 p-2">
            <DroppableQuestionList
              questions={filterDeleted}
              selectedIndex={selectedIndex}
              onQuestionSelect={handleQuestionSelect}
              onQuestionDelete={handleQuestionDelete}
              onQuestionMove={handleQuestionMove}
              onQuestionAdd={handleQuestionAdd}
            />
          </main>

          {/* 右侧属性面板 */}
          <div className="w-80 overflow-y-auto h-full shrink-0">
            <aside className="w-full h-full rounded-2xl bg-card/70 shadow-md p-4 backdrop-blur border border-border">
              <h2 className="text-base font-semibold mb-4 text-card-foreground">
                属性设置
              </h2>
              {questionnaire && (
                <PropertyPanel
                  questionnaire={questionnaire}
                  questions={filterDeleted}
                  onQuestionnaireEdit={updateQuestionnaireProps}
                  selectedQuestion={
                    selectedIndex !== null && filterDeleted[selectedIndex]
                      ? filterDeleted[selectedIndex]
                      : (undefined as any)
                  }
                  onQuestionEdit={handleQuestionEdit}
                />
              )}
            </aside>
          </div>
        </div>

      </div>
    </DndProvider>
  );
}
 