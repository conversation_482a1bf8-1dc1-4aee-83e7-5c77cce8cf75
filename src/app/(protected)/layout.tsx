'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { useUserStore } from '@/store/user';

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { user, isInitialized, isLoading, isProcessing } = useUserStore();
  const [shouldRedirect, setShouldRedirect] = useState(false);

  // 使用useCallback来稳定化检查逻辑
  const checkAuthStatus = useCallback(() => {
    // 等待初始化完成
    if (!isInitialized) return;

    // 检查是否有用户信息
    if (!user) {
      // 检查是否有 token
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.log('No user and no token, redirecting to login...');
        setShouldRedirect(true);
      } else {
        // 有 token 但没有用户信息，可能是 token 过期，清除 token
        console.log('Has token but no user, clearing token...');
        localStorage.removeItem('access_token');
        setShouldRedirect(true);
      }
    } else {
      // 有用户信息，确保不重定向
      setShouldRedirect(false);
    }
  }, [user, isInitialized]);

  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  useEffect(() => {
    if (shouldRedirect && !isProcessing) {
      console.log('Redirecting to login page...');
      router.replace('/login');
    }
  }, [shouldRedirect, router, isProcessing]);

  // 如果还在初始化或加载中，显示加载状态
  if (!isInitialized || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">加载中...</p>
        </div>
      </div>
    );
  }

  // 如果应该重定向，显示重定向状态
  if (shouldRedirect) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">正在重定向到登录页面...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 