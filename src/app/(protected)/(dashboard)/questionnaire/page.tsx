"use client";

import { questionnaire<PERSON><PERSON>, Questionnaire<PERSON>ageP<PERSON><PERSON>, CreateQuestionnaireRequest, UpdateQuestionnaireRequest } from "@/lib/api/questionnaire";
import { QuestionnaireResponseDTO, QuestionnaireType, QuestionnaireStatus } from "@/types/questionnaire";
import {
  ColumnDef,
} from "@tanstack/react-table";
import { DataTable } from "@/components/business/data-table";
import { QuestionnaireForm } from "@/components/business/create-questionnaire-form";
import { useTable } from "@/hooks/use-table";
import { Button } from "@/components/ui/button";
import { Edit, FileText, Trash2, Plus, Upload, Ban } from "lucide-react";
import { useState } from "react";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertD<PERSON>og<PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { AiChatDialog } from "@/components/business/ai-chat-dialog";

// 禁用SSR，确保组件只在客户端渲染
export const dynamic = 'force-dynamic';

function getTypeLabel(type: QuestionnaireType) {
  switch (type) {
    case QuestionnaireType.Questionnaire:
      return "问卷";
    case QuestionnaireType.Form:
      return "表单";
    default:
      return type;
  }
}

function getStatusLabel(status: QuestionnaireStatus) {
  switch (status) {
    case QuestionnaireStatus.Draft:
      return "草稿";
    case QuestionnaireStatus.Published:
      return "发布";
    case QuestionnaireStatus.Disabled:
      return "停用";
    default:
      return status;
  }
}

export default function QuestionnairePage() {
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [editingQuestionnaire, setEditingQuestionnaire] = useState<QuestionnaireResponseDTO | null>(null);
  const [isAiOpen, setIsAiOpen] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();

  const createMutation = useMutation({
    mutationFn: questionnaireApi.create,
    onSuccess: () => {
      toast.success("问卷创建成功");
      setIsCreateOpen(false);
      queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
    },
    onError: (error) => {
      toast.error("创建失败: " + error.message);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateQuestionnaireRequest }) => 
      questionnaireApi.update(id, data),
    onSuccess: () => {
      toast.success("问卷更新成功");
      setIsEditOpen(false);
      setEditingQuestionnaire(null);
      queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
    },
    onError: (error) => {
      toast.error("更新失败: " + error.message);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: questionnaireApi.delete,
    onSuccess: () => {
      toast.success("问卷删除成功");
      queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
    },
    onError: (error) => {
      toast.error("删除失败: " + error.message);
    },
  });

  const publishMutation = useMutation({
    mutationFn: questionnaireApi.publish,
    onSuccess: () => {
      toast.success("问卷已发布");
      queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
    },
    onError: (error) => {
      toast.error("发布失败: " + error.message);
    },
  });

  const disableMutation = useMutation({
    mutationFn: questionnaireApi.disable,
    onSuccess: () => {
      toast.success("问卷已停用");
      queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
    },
    onError: (error) => {
      toast.error("停用失败: " + error.message);
    },
  });

  const handleCreate = (data: CreateQuestionnaireRequest) => {
    createMutation.mutate(data);
  };

  const handleEdit = (data: UpdateQuestionnaireRequest) => {
    if (editingQuestionnaire) {
      updateMutation.mutate({ id: editingQuestionnaire.id, data });
    }
  };

  const handleDelete = (id: string) => {
    deleteMutation.mutate(id);
  };

  const handleEditClick = (questionnaire: QuestionnaireResponseDTO) => {
    setEditingQuestionnaire(questionnaire);
    setIsEditOpen(true);
  };

  const handleEditCancel = () => {
    setIsEditOpen(false);
    setEditingQuestionnaire(null);
  };

  const columns: ColumnDef<QuestionnaireResponseDTO>[] = [
    {
      accessorKey: "id",
      header: "ID",
    },
    {
      accessorKey: "title",
      header: "标题",
    },
    {
      accessorKey: "type",
      header: "类型",
      cell: ({ row }) => getTypeLabel(
        row.getValue("type")
      ),
    },
    {
      accessorKey: "questionNum",
      header: "题目数",
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => getStatusLabel(
        row.getValue("status")
      ),
    },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }) => {
        const questionnaire = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditClick(questionnaire)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // 跳转到编辑问卷题目页面
                router.push(`/questionnaire/${questionnaire.id}`);
              }}
            >
              <FileText className="h-4 w-4" />
            </Button>
            {/* 发布/停用按钮 */}
            {(questionnaire.status === QuestionnaireStatus.Draft || questionnaire.status === QuestionnaireStatus.Disabled) && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Upload className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>确认发布</AlertDialogTitle>
                    <AlertDialogDescription>
                      确认要发布问卷 "{questionnaire.title}" 吗？
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => publishMutation.mutate(questionnaire.id)}
                    >
                      发布
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            {questionnaire.status === QuestionnaireStatus.Published && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Ban className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>确认停用</AlertDialogTitle>
                    <AlertDialogDescription>
                      确认要停用问卷 "{questionnaire.title}" 吗？
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => disableMutation.mutate(questionnaire.id)}
                    >
                      停用
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认删除</AlertDialogTitle>
                  <AlertDialogDescription>
                    您确定要删除问卷 "{questionnaire.title}" 吗？此操作无法撤销。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={() => {
                      handleDelete(questionnaire.id);
                    }}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    删除
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
  ];

  const { isLoading, table } = useTable<QuestionnaireResponseDTO, QuestionnairePageParams>(
    questionnaireApi.getPage,
    {
      queryKey: "questionnaires",
      columns: columns,
      defaultParams: {
        page: 0,
        size: 10,
        title: null,
      },
    }
  );

  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h2 className="text-3xl font-bold tracking-tight">问卷列表</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsAiOpen(true)}>
            <span className="mr-2">🤖</span>AI智能
          </Button>
          <Sheet open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <SheetTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新增问卷
              </Button>
            </SheetTrigger>
            <SheetContent className="w-[400px] sm:w-[540px]">
              <SheetHeader>
                <SheetTitle>新增问卷</SheetTitle>
              </SheetHeader>
              <QuestionnaireForm<CreateQuestionnaireRequest>
                mode="create"
                onSubmit={handleCreate}
              />
            </SheetContent>
          </Sheet>
        </div>
        {isAiOpen && (
          <AiChatDialog open={isAiOpen} onOpenChange={setIsAiOpen} />
        )}
      </div>

      {/* 编辑问卷的Sheet */}
      <Sheet open={isEditOpen} onOpenChange={setIsEditOpen}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>编辑问卷</SheetTitle>
          </SheetHeader>
          {editingQuestionnaire && (
            <QuestionnaireForm<UpdateQuestionnaireRequest>
              mode="edit"
              initialData={editingQuestionnaire}
              onSubmit={handleEdit}
              onCancel={handleEditCancel}
            />
          )}
        </SheetContent>
      </Sheet>

      <DataTable isLoading={isLoading} table={table} />
    </div>
  );
} 