import { create } from 'zustand';
import { toast } from 'sonner';

// 任务状态枚举
export enum TaskStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING", 
  COMPLETED = "COMPLETED",
  FAILED = "FAILED"
}

// 任务信息接口
export interface AITask {
  taskId: string;
  status: TaskStatus;
  content?: string;
  error?: string;
  createdAt: number;
}

// SSE 事件数据接口
export interface SSEEventData {
  taskId: string;
  status: TaskStatus;
  content?: string;
  error?: string;
}

interface AITaskState {
  // 当前活跃的任务
  activeTasks: Map<string, AITask>;
  // SSE 连接管理
  sseConnections: Map<string, EventSource>;
  
  // 操作方法
  addTask: (taskId: string) => void;
  updateTask: (taskId: string, updates: Partial<AITask>) => void;
  removeTask: (taskId: string) => void;
  connectSSE: (taskId: string) => void;
  disconnectSSE: (taskId: string) => void;
  disconnectAllSSE: () => void;
  getTask: (taskId: string) => AITask | undefined;
}

export const useAITaskStore = create<AITaskState>((set, get) => ({
  activeTasks: new Map(),
  sseConnections: new Map(),

  addTask: (taskId: string) => {
    set((state) => {
      const newTasks = new Map(state.activeTasks);
      newTasks.set(taskId, {
        taskId,
        status: TaskStatus.PENDING,
        createdAt: Date.now(),
      });
      return { activeTasks: newTasks };
    });
  },

  updateTask: (taskId: string, updates: Partial<AITask>) => {
    set((state) => {
      const newTasks = new Map(state.activeTasks);
      const existingTask = newTasks.get(taskId);
      if (existingTask) {
        newTasks.set(taskId, { ...existingTask, ...updates });
      }
      return { activeTasks: newTasks };
    });
  },

  removeTask: (taskId: string) => {
    set((state) => {
      const newTasks = new Map(state.activeTasks);
      newTasks.delete(taskId);
      
      // 同时断开 SSE 连接
      const newConnections = new Map(state.sseConnections);
      const connection = newConnections.get(taskId);
      if (connection) {
        connection.close();
        newConnections.delete(taskId);
      }
      
      return { 
        activeTasks: newTasks,
        sseConnections: newConnections 
      };
    });
  },

  connectSSE: (taskId: string) => {
    const { sseConnections, updateTask } = get();
    
    // 如果已经有连接，先关闭
    const existingConnection = sseConnections.get(taskId);
    if (existingConnection) {
      existingConnection.close();
    }

    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "";
    const token = localStorage.getItem("access_token") || "";
    const url = `${baseUrl}/api/subscribe/${taskId}?token=${encodeURIComponent(token)}`;
    
    const eventSource = new EventSource(url);

    eventSource.onopen = () => {
      console.log(`SSE 连接已建立: ${taskId}`);
      updateTask(taskId, { status: TaskStatus.PROCESSING });
    };

    eventSource.onmessage = (event) => {
      try {
        const data: SSEEventData = JSON.parse(event.data);
        console.log(`收到 SSE 消息 ${taskId}:`, data);
        
        updateTask(taskId, {
          status: data.status,
          content: data.content,
          error: data.error,
        });
        
        if (data.status === TaskStatus.COMPLETED) {
          toast.success("AI 生成完成");
          // 可以选择自动断开连接
          setTimeout(() => {
            get().disconnectSSE(taskId);
          }, 1000);
        } else if (data.status === TaskStatus.FAILED) {
          toast.error(`AI 生成失败: ${data.error || "未知错误"}`);
          setTimeout(() => {
            get().disconnectSSE(taskId);
          }, 1000);
        }
      } catch (error) {
        console.error(`解析 SSE 消息失败 ${taskId}:`, error);
      }
    };

    eventSource.onerror = (error) => {
      console.error(`SSE 连接错误 ${taskId}:`, error);
      updateTask(taskId, { status: TaskStatus.FAILED, error: "连接中断" });
      toast.error("连接中断，请重试");
    };

    // 保存连接
    set((state) => {
      const newConnections = new Map(state.sseConnections);
      newConnections.set(taskId, eventSource);
      return { sseConnections: newConnections };
    });
  },

  disconnectSSE: (taskId: string) => {
    set((state) => {
      const newConnections = new Map(state.sseConnections);
      const connection = newConnections.get(taskId);
      if (connection) {
        connection.close();
        newConnections.delete(taskId);
      }
      return { sseConnections: newConnections };
    });
  },

  disconnectAllSSE: () => {
    set((state) => {
      state.sseConnections.forEach((connection) => {
        connection.close();
      });
      return { sseConnections: new Map() };
    });
  },

  getTask: (taskId: string) => {
    return get().activeTasks.get(taskId);
  },
}));

// 清理过期任务的工具函数
export const cleanupExpiredTasks = () => {
  const { activeTasks, removeTask } = useAITaskStore.getState();
  const now = Date.now();
  const expireTime = 30 * 60 * 1000; // 30分钟过期
  
  activeTasks.forEach((task, taskId) => {
    if (now - task.createdAt > expireTime) {
      removeTask(taskId);
    }
  });
};
