import { create } from 'zustand';
import { UserInfo, AccessTokenDTO } from '@/types/auth';
import { authApi } from '@/lib/api/auth';

interface UserState {
  user: UserInfo | null;
  isLoading: boolean;
  isInitialized: boolean;
  isProcessing: boolean;
  lastFetchTime: number | null; // 添加最后获取时间
  
  // Actions
  setUser: (user: UserInfo | null) => void;
  setLoading: (loading: boolean) => void;
  setProcessing: (processing: boolean) => void;
  fetchCurrentUser: (force?: boolean) => Promise<void>;
  loginWithCode: (code: string) => Promise<void>;
  logout: () => Promise<void>;
  clearUser: () => void;
  initialize: () => Promise<void>;
}

// 缓存时间：5分钟
const CACHE_DURATION = 5 * 60 * 1000;

export const useUserStore = create<UserState>((set, get) => ({
  user: null,
  isLoading: false,
  isInitialized: false,
  isProcessing: false,
  lastFetchTime: null,

  setUser: (user) => set({ user }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  setProcessing: (isProcessing) => set({ isProcessing }),
  
  fetchCurrentUser: async (force = false) => {
    const { setUser, setLoading, user, lastFetchTime } = get();
    
    // 如果已经有用户信息且不在强制刷新模式下，检查缓存
    if (user && !force && lastFetchTime) {
      const now = Date.now();
      if (now - lastFetchTime < CACHE_DURATION) {
        console.log('User info is cached, skipping fetch');
        return;
      }
    }
    
    try {
      setLoading(true);
      const user = await authApi.getCurrentUser();
      setUser(user);
      set({ lastFetchTime: Date.now() });
    } catch (error) {
      console.error('Failed to fetch current user:', error);
      setUser(null);
      // 如果获取用户信息失败，可能是 token 过期，清除本地存储
      localStorage.removeItem('access_token');
    } finally {
      setLoading(false);
    }
  },

  loginWithCode: async (code: string) => {
    const { setUser, setLoading, setProcessing, fetchCurrentUser } = get();
    
    // 如果正在处理中，直接返回
    if (get().isProcessing) {
      console.log('Login already in progress, skipping...');
      return;
    }

    try {
      setProcessing(true);
      setLoading(true);
      
      console.log('Starting login with code:', code);
      
      // 使用授权码登录
      const accessToken: AccessTokenDTO = await authApi.loginWithCode(code);
      
      // 保存 token
      localStorage.setItem('access_token', accessToken.token);
      
      // 获取用户信息（强制刷新）
      await fetchCurrentUser(true);
      
      console.log('Login completed successfully');
      
    } catch (error) {
      console.error('Login failed:', error);
      setUser(null);
      localStorage.removeItem('access_token');
      throw error;
    } finally {
      setLoading(false);
      setProcessing(false);
    }
  },

  logout: async () => {
    const { setUser, setLoading } = get();
    
    try {
      setLoading(true);
      
      // 调用后端退出接口
      await authApi.logout();
      
      console.log('Logout successful');
      
    } catch (error) {
      console.error('Logout failed:', error);
      // 即使后端退出失败，也要清除本地状态
    } finally {
      // 无论后端是否成功，都清除本地状态
      setUser(null);
      set({ lastFetchTime: null });
      localStorage.removeItem('access_token');
      
      // 清理所有 cookies
      if (typeof document !== 'undefined') {
        // 获取所有 cookies
        const cookies = document.cookie.split(';');
        
        // 清除每个 cookie
        cookies.forEach(cookie => {
          const eqPos = cookie.indexOf('=');
          const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
          
          // 清除 cookie（设置过期时间为过去的时间）
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
        });
        
        console.log('All cookies cleared');
      }
      
      setLoading(false);
    }
  },

  clearUser: () => {
    set({ user: null, lastFetchTime: null });
    localStorage.removeItem('access_token');
  },

  initialize: async () => {
    const { fetchCurrentUser } = get();
    
    // 检查是否有 token
    const token = localStorage.getItem('access_token');
    if (token) {
      // 如果有 token，尝试获取用户信息
      await fetchCurrentUser();
    }
    
    set({ isInitialized: true });
  },
})); 