import { create } from 'zustand';
import { QuestionnaireResponseDTO, QuestionResponseDTO, QuestionProps, QuestionLogicExpression } from '@/types/questionnaire';

interface QuestionEditState {
  questionnaire: QuestionnaireResponseDTO | null;
  history: { version: number; questionnaire: QuestionnaireResponseDTO | null }[];
  redoStack: { version: number; questionnaire: QuestionnaireResponseDTO | null }[];
  setQuestionnaire: (q: QuestionnaireResponseDTO, isFromServer?: boolean) => void;
  addQuestion: (q: QuestionResponseDTO, index: number) => void;
  updateQuestion: (id: number | string, propsUpdate: Partial<QuestionProps>) => void;
  removeQuestion: (id: number | string) => void;
  moveQuestion: (from: number, to: number) => void;
  resetQuestions: () => void;
  undo: () => void;
  redo: () => void;
  resetHistory: () => void;
  setFullState: (payload: Partial<QuestionEditState>) => void;
  updateQuestionnaireProps: (field: keyof QuestionnaireResponseDTO, value: any) => void;
  updateLogicExpressions: (logicExpressions: QuestionLogicExpression[]) => void;
}

const MAX_HISTORY = 200;

export const useQuestionEditStore = create<QuestionEditState>((set, get) => ({
  questionnaire: null,
  history: [],
  redoStack: [],
  setQuestionnaire: (q, isFromServer) => set(state => {
    if (isFromServer) {
      return {
        questionnaire: q,
        history: [],
        redoStack: []
      };
    }
    const newHistory = [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    return {
      history: newHistory.length > MAX_HISTORY ? newHistory.slice(-MAX_HISTORY) : newHistory,
      redoStack: [],
      questionnaire: q
    };
  }),
  addQuestion: (q, index) => set(state => {
    if (!state.questionnaire) return state;
    let newQuestions = [...state.questionnaire.questions];
    // 处理插入点及其后 sortOrder+1，crud=4→2
    newQuestions = newQuestions.map((item, i) => {
      if (i >= index) {
        return {
          ...item,
          sortOrder: (item.sortOrder ?? i) + 1,
          crud: item.crud === 4 ? 2 : item.crud
        };
      }
      return item;
    });
    // 新增题目 sortOrder=index
    const newQuestion = { ...q, sortOrder: index };
    newQuestions.splice(index, 0, newQuestion);
    // 重新保证 sortOrder 连续
    newQuestions = newQuestions.map((item, i) => ({ ...item, sortOrder: i }));
    const newQuestionnaire = { ...state.questionnaire, questions: newQuestions };
    const newHistory = state.history.length === 0 
      ? [{ version: 1, questionnaire: state.questionnaire }]
      : [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    return {
      history: newHistory.length > MAX_HISTORY ? newHistory.slice(-MAX_HISTORY) : newHistory,
      redoStack: [],
      questionnaire: newQuestionnaire
    };
  }),
  updateQuestion: (id, propsUpdate) => set(state => {
    if (!state.questionnaire) return state;
    const currentHistory = state.history.length === 0 
      ? [{ version: 1, questionnaire: state.questionnaire }]
      : [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    const newQuestions = state.questionnaire.questions.map(item =>
      item.id === id
        ? {
            ...item,
            props: { ...item.props, ...propsUpdate },
            crud: item.crud === 1 ? 1 : 2 as 1 | 2 | 3 | 4 
          }
        : item
    );
    return {
      history: currentHistory.length > MAX_HISTORY ? currentHistory.slice(-MAX_HISTORY) : currentHistory,
      redoStack: [],
      questionnaire: { ...state.questionnaire, questions: newQuestions }
    };
  }),
  removeQuestion: (id) => set(state => {
    if (!state.questionnaire) return state;
    const currentHistory = state.history.length === 0 
      ? [{ version: 1, questionnaire: state.questionnaire }]
      : [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    const filtered = state.questionnaire.questions.filter(item => {
      if (item.id !== id) return true;
      if (item.crud === 1 || (typeof item.id === 'string' && item.id.startsWith('local_'))) {
        return false;
      }
      return true;
    });
    const newQuestions = filtered.map(item => {
      const safeItem = { ...item, crud: (item.crud ?? 2) as 1 | 2 | 3 | 4  } as QuestionResponseDTO;
      if (safeItem.id !== id) return safeItem;
      return { ...safeItem, crud: 3 as 3 };
    });
    return {
      history: currentHistory.length > MAX_HISTORY ? currentHistory.slice(-MAX_HISTORY) : currentHistory,
      redoStack: [],
      questionnaire: { ...state.questionnaire, questions: newQuestions }
    };
  }),
  moveQuestion: (from, to) => set(state => {
    if (!state.questionnaire) return state;
    let arr = [...state.questionnaire.questions];
    const [moved] = arr.splice(from, 1);
    arr.splice(to, 0, moved);
    // 重新赋值 sortOrder，所有题目 crud=4→2
    arr = arr.map((item, i) => ({
      ...item,
      sortOrder: i,
      crud: item.crud === 4 ? 2 : item.crud
    }));
    const newHistory = state.history.length === 0 
      ? [{ version: 1, questionnaire: state.questionnaire }]
      : [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    return {
      history: newHistory.length > MAX_HISTORY ? newHistory.slice(-MAX_HISTORY) : newHistory,
      redoStack: [],
      questionnaire: { ...state.questionnaire, questions: arr }
    };
  }),
  resetQuestions: () => set(state => {
    if (!state.questionnaire) return state;
    const currentHistory = state.history.length === 0 
      ? [{ version: 1, questionnaire: state.questionnaire }]
      : [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    return {
      history: currentHistory.length > MAX_HISTORY ? currentHistory.slice(-MAX_HISTORY) : currentHistory,
      redoStack: [],
      questionnaire: { ...state.questionnaire, questions: [] }
    };
  }),
  undo: () => set(state => {
    if (state.history.length === 0) return state;
    const prev = state.history[state.history.length - 1];
    const newHistory = state.history.slice(0, -1);
    return {
      questionnaire: prev.questionnaire,
      history: newHistory,
      redoStack: [{ version: state.history.length, questionnaire: state.questionnaire }, ...state.redoStack],
    };
  }),
  redo: () => set(state => {
    if (state.redoStack.length === 0) return state;
    const [next, ...restRedo] = state.redoStack;
    const newHistory = [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    return {
      questionnaire: next.questionnaire,
      history: newHistory.length > MAX_HISTORY ? newHistory.slice(-MAX_HISTORY) : newHistory,
      redoStack: restRedo,
    };
  }),
  resetHistory: () => set(state => ({
    questionnaire: null,
    history: [],
    redoStack: [],
  })),
  setFullState: (payload) => set((state) => ({
    ...state,
    ...payload,
  })),
  updateQuestionnaireProps: (field, value) => set(state => {
    if (!state.questionnaire) return state;
    const newQuestionnaire = { ...state.questionnaire, [field]: value };
    const newHistory = state.history.length === 0
      ? [{ version: 1, questionnaire: state.questionnaire }]
      : [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    return {
      questionnaire: newQuestionnaire,
      history: newHistory.length > MAX_HISTORY ? newHistory.slice(-MAX_HISTORY) : newHistory,
      redoStack: [],
    };
  }),
  updateLogicExpressions: (logicExpressions) => set(state => {
    console.log("updateLogicExpressions", logicExpressions)
    if (!state.questionnaire) return state;
    const newQuestionnaire = { ...state.questionnaire, logicExpressions };
    const newHistory = state.history.length === 0
      ? [{ version: 1, questionnaire: state.questionnaire }]
      : [...state.history, { version: state.history.length + 1, questionnaire: state.questionnaire }];
    return {
      questionnaire: newQuestionnaire,
      history: newHistory.length > MAX_HISTORY ? newHistory.slice(-MAX_HISTORY) : newHistory,
      redoStack: [],
    };
  }),
})); 