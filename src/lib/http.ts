import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";
import { toast } from "sonner";
import { Result } from "@/types/common";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

class HttpService {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 120000,
      headers: {
        "Content-Type": "application/json",
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 从 localStorage 获取 token 并添加到请求头
        const token = localStorage.getItem("access_token");
        if (token) {
          config.headers["Qs-Authorization"] = `Bearer ${token}`;
        }
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error: AxiosError) => {
        this.handleError(error);
        return Promise.reject(error);
      }
    );
  }

  private handleError(error: AxiosError) {
    if (error.response) {
      const { status, data } = error.response;

      // 401 未授权，重定向到登录页面
      if (status === 401) {
        localStorage.removeItem("access_token");
        if (typeof window !== "undefined") {
          // 清理所有 cookies
          if (typeof document !== "undefined") {
            // 获取所有 cookies
            const cookies = document.cookie.split(";");

            // 清除每个 cookie
            cookies.forEach((cookie) => {
              const eqPos = cookie.indexOf("=");
              const name =
                eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

              // 清除 cookie（设置过期时间为过去的时间）
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
            });

            console.log("All cookies cleared");
          }
          window.location.href = "/login";
        }
        return;
      }

      // 显示错误消息
      const errorMessage =
        (data as { message?: string })?.message || `请求失败 (${status})`;
      toast.error(errorMessage);
    } else if (error.request) {
      // 网络错误
      toast.error("网络连接失败，请检查网络设置");
    } else {
      // 其他错误
      toast.error("请求配置错误");
    }
  }

  // 通用请求方法
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.request(config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // GET 请求
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: "GET", url });
  }

  // POST 请求
  async post<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.request<T>({ ...config, method: "POST", url, data });
  }

  // PUT 请求
  async put<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.request<T>({ ...config, method: "PUT", url, data });
  }

  // DELETE 请求
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: "DELETE", url });
  }

  // 处理业务逻辑的通用方法
  async requestWithBusinessLogic<T>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.request<Result<T>>(config);

    // 检查业务状态码
    if (response.code === 200) {
      return response.data as T;
    } else {
      // 业务逻辑错误，显示错误消息
      toast.error(response.message || "操作失败");
      throw new Error(response.message || "操作失败");
    }
  }
}

// 创建单例实例
export const httpService = new HttpService();

// 导出便捷方法
export const http = {
  get: <T>(url: string, config?: AxiosRequestConfig) =>
    httpService.get<T>(url, config),
  post: <T>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
    httpService.post<T>(url, data, config),
  put: <T>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
    httpService.put<T>(url, data, config),
  delete: <T>(url: string, config?: AxiosRequestConfig) =>
    httpService.delete<T>(url, config),
  requestWithBusinessLogic: <T>(config: AxiosRequestConfig) =>
    httpService.requestWithBusinessLogic<T>(config),
};
