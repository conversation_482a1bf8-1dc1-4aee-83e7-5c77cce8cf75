import { AccessTokenDTO, UserInfo } from "@/types/auth";
import { http } from "../http";

export const authApi = {
  /**
   * 使用授权码登录
   * @param code OAuth 授权码
   * @returns 访问令牌
   */
  loginWithCode: async (code: string): Promise<AccessTokenDTO> => {
    return http.requestWithBusinessLogic<AccessTokenDTO>({
      method: 'GET',
      url: `/oauth/codeLogin?code=${code}`,
    });
  },

  /**
   * 刷新访问令牌
   * @param refreshToken 刷新令牌
   * @returns 新的访问令牌
   */
  refreshToken: async (refreshToken: string): Promise<AccessTokenDTO> => {
    return http.requestWithBusinessLogic<AccessTokenDTO>({
      method: 'POST',
      url: '/oauth/refresh',
      data: { refreshToken },
    });
  },

  /**
   * 登出
   */
  logout: async (): Promise<void> => {
    return http.requestWithBusinessLogic<void>({
      method: 'GET',
      url: '/oauth/logout',
    });
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: async (): Promise<UserInfo> => {
    return http.requestWithBusinessLogic<UserInfo>({
      method: 'GET',
      url: '/oauth/userinfo',
    });
  },
}; 