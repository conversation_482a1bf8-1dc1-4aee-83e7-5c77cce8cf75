import { http } from "../http";
import { QuestionnaireResponseDTO, QuestionnaireType, QuestionnaireStatus, QuestionResponseDTO, QuestionLogicExpression } from "@/types/questionnaire";
import { PageResult, PageRequest } from "@/types/common"

export interface QuestionnairePageParams extends PageRequest {
    title: string;
}

export interface CreateQuestionnaireRequest {
    title: string;
    type: QuestionnaireType;
    description?: string;
    status?: QuestionnaireStatus;
    startTime?: string;
    endTime?: string;
    isAnonymous?: boolean;
    allowMultiple?: boolean;
    maxSubmissions?: number;
}

export interface UpdateQuestionnaireRequest {
    title: string;
    description?: string;
    status?: QuestionnaireStatus;
    startTime?: string;
    endTime?: string;
    isAnonymous?: boolean;
    allowMultiple?: boolean;
    maxSubmissions?: number;
}

export interface UpdateQuestionnaireQuestionsRequest {
  questionnaireId: string
  questions: QuestionResponseDTO[];
  logicExpressions?: QuestionLogicExpression[];
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
}

export interface LlmRequest {
  content: string;
  conversationId?: string;
  inputs?: Record<string, any>;
}

export interface FormatTaskCreateResponse {
  taskId: string;
}

export const questionnaireApi = {
  /**
   * 分页查询问卷列表
   */
  getPage: async (params: QuestionnairePageParams): Promise<PageResult<QuestionnaireResponseDTO>> => {
    return http.requestWithBusinessLogic<PageResult<QuestionnaireResponseDTO>>({
      method: 'POST',
      url: '/questionnaires/pages',
      data: params,
    });
  },

  /**
   * 新增问卷
   */
  create: async (data: CreateQuestionnaireRequest): Promise<QuestionnaireResponseDTO> => {
    return http.requestWithBusinessLogic<QuestionnaireResponseDTO>({
      method: 'POST',
      url: '/questionnaires',
      data,
    });
  },

  /**
   * 更新问卷
   */
  update: async (id: string, data: UpdateQuestionnaireRequest): Promise<QuestionnaireResponseDTO> => {
    return http.requestWithBusinessLogic<QuestionnaireResponseDTO>({
      method: 'PUT',
      url: `/questionnaires/${id}`,
      data,
    });
  },

  /**
   * 删除问卷
   */
  delete: async (id: string): Promise<boolean> => {
    return http.requestWithBusinessLogic<boolean>({
      method: 'DELETE',
      url: `/questionnaires/${id}`,
    });
  },

  /**
   * 获取问卷详情
   */
  getById: async (id: String): Promise<QuestionnaireResponseDTO> => {
    return http.requestWithBusinessLogic<QuestionnaireResponseDTO>({
      method: 'GET',
      url: `/questionnaires/${id}`,
    });
  },

  /**
   * 更新问卷题目
   */
  updateQuestions: async (data: UpdateQuestionnaireQuestionsRequest): Promise<QuestionnaireResponseDTO> => {
    return http.requestWithBusinessLogic<QuestionnaireResponseDTO>({
      method: 'POST',
      url: `/questions/batch-update`,
      data,
    });
  },

  /**
   * 发布问卷
   */
  publish: async (id: string): Promise<QuestionnaireResponseDTO> => {
    return http.requestWithBusinessLogic<QuestionnaireResponseDTO>({
      method: 'POST',
      url: `/api/questionnaires/${id}/publish`,
    });
  },

  /**
   * 停用问卷
   */
  disable: async (id: string): Promise<QuestionnaireResponseDTO> => {
    return http.requestWithBusinessLogic<QuestionnaireResponseDTO>({
      method: 'POST',
      url: `/questionnaires/${id}/disable`,
    });
  },
  dataFormatAnalyze: async (req: LlmRequest): Promise<FormatTaskCreateResponse> => {
    return http.requestWithBusinessLogic<FormatTaskCreateResponse>({
      method: 'POST',
      url: '/format-tasks/start',
      data: req,
    });
  },
  // 你可以继续添加其它问卷相关接口
}; 