import {
  QuestionType,
  QuestionModule,
  QuestionProps,
  OptionItem,
  TextQuestionProps,
  InputQuestionProps,
  TextareaQuestionProps,
  InputNumberQuestionProps,
  RadioInputQuestionProps,
  CheckboxInputQuestionProps,
  RadioQuestionProps,
  CheckboxQuestionProps,
  SelectQuestionProps,
  SliderQuestionProps,
  DateQuestionProps,
  DateTimeQuestionProps,
  TimeQuestionProps,
  YearQuestionProps,
  MonthQuestionProps,
  ProvinceQuestionProps,
  ProvinceCityQuestionProps,
  AreaQuestionProps,
  SexQuestionProps,
  QuestionTypeRegistryType,
  RenderFnProps,
} from "@/types/questionnaire";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { genUUID } from "@/lib/utils";
import { SexIcon } from "@/components/business/QuestionTypeIcons";
import { useState } from "react";



export function RadioInputRender({ props, value, onChange }: RenderFnProps<RadioInputQuestionProps>) {
  // value: { value: string, input?: string }
  const [selected, setSelected] = useState(value?.value || "");
  const [inputValue, setInputValue] = useState(value?.input || "");

  // 选项切换时清空补充输入
  const handleChange = (val: string) => {
    setSelected(val);
    if (props.options.find((opt: OptionItem) => opt.value === val)?.hasInput) {
      setInputValue("");
      onChange?.({ value: val, input: "" });
    } else {
      setInputValue("");
      onChange?.({ value: val });
    }
  };

  return (
    <RadioGroup value={selected} onValueChange={handleChange}>
      {props.options.map((option: OptionItem) => (
        <div key={option.value} className="flex items-center space-x-2">
          <RadioGroupItem value={option.value} id={`radio-${option.value}`} />
          <Label htmlFor={`radio-${option.value}`}>{option.label}</Label>
          {option.hasInput && selected === option.value && (
            <Input
              className="ml-2 w-40"
              value={inputValue}
              onChange={e => {
                setInputValue(e.target.value);
                onChange?.({ value: selected, input: e.target.value });
              }}
              placeholder="请输入补充内容"
            />
          )}
        </div>
      ))}
    </RadioGroup>
  );
}

export const QuestionTypeRegistry: QuestionTypeRegistryType = {
  // 提示类
  [QuestionType.Text]: {
    key: QuestionType.Text,
    moduleName: QuestionModule.Tip,
    label: "文本",
    icon: "📝",
    preview: () => "文本",
    render: ({ props }: { props: TextQuestionProps }) => (
      <div
        className="text-foreground select-none pointer-events-none"
        style={{
          fontSize: props.fontSize ? props.fontSize + "px" : undefined,
          fontWeight: props.fontWeight || undefined,
          color: props.color || undefined,
        }}
      >
        {props.title || "文本内容"}
      </div>
    ),
    props: [
      {
        name: "title",
        label: "文本内容",
        type: "input",
        defaultValue: "文本内容",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "fontSize",
        label: "字体大小",
        type: "number",
        defaultValue: 16,
      },
      {
        name: "fontWeight",
        label: "字重",
        type: "number",
        defaultValue: 400,
      },
      {
        name: "color",
        label: "字体颜色",
        type: "color",
        defaultValue: "#333333",
      },
    ],
  },

  // 输入类
  [QuestionType.Input]: {
    key: QuestionType.Input,
    moduleName: QuestionModule.Input,
    label: "单行文本",
    icon: "🔤",
    preview: () => "单行文本",
    render: ({ props }: { props: InputQuestionProps }) => (
      <div className="pointer-events-none flex justify-center items-center">
        <Input placeholder={props.placeholder || "请输入"} />
        {props.unit && <span className="ml-4">{props.unit}</span>}
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "单行文本标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "unit", label: "答案单位", type: "input", defaultValue: "" },
      {
        name: "placeholder",
        label: "输入提示语",
        type: "input",
        defaultValue: "请输入",
      },
    ],
  },

  [QuestionType.Textarea]: {
    key: QuestionType.Textarea,
    moduleName: QuestionModule.Input,
    label: "多行文本",
    icon: "📄",
    preview: () => "多行文本",
    render: ({ props }: { props: TextareaQuestionProps }) => (
      <Textarea
        placeholder={props?.placeholder || "请输入"}
        className="pointer-events-none"
      />
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "多行文本标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      {
        name: "placeholder",
        label: "输入提示语",
        type: "input",
        defaultValue: "请输入",
      },
    ],
  },

  [QuestionType.InputNumber]: {
    key: QuestionType.InputNumber,
    moduleName: QuestionModule.Input,
    label: "数字输入",
    icon: "🔢",
    preview: () => "数字输入",
    render: ({ props }: { props: InputNumberQuestionProps }) => (
      <div className="pointer-events-none flex justify-center items-center">
        <Input type="number" placeholder={props.placeholder || "请输入"} />
        {props.unit && <span className="ml-4">{props.unit}</span>}
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "数字输入标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "unit", label: "答案单位", type: "input", defaultValue: "" },
      {
        name: "placeholder",
        label: "输入提示语",
        type: "input",
        defaultValue: "请输入",
      },
      { name: "min", label: "最小值", type: "number" },
      { name: "max", label: "最大值", type: "number" },
    ],
  },

  [QuestionType.RadioInput]: {
    key: QuestionType.RadioInput,
    moduleName: QuestionModule.Input,
    label: "单选输入",
    icon: "🔘",
    preview: () => "单选输入",
    render: ({ props, value, onChange }: RenderFnProps<RadioInputQuestionProps>) => (
      <RadioInputRender props={props} value={value} onChange={onChange} />
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "单选输入标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      {
        name: "options",
        label: "选项列表",
        type: "options",
        defaultValue: [
          { label: "选项1", value: genUUID() },
          { label: "选项2", value: genUUID() },
        ],
      },
    ],
  },

  [QuestionType.CheckboxInput]: {
    key: QuestionType.CheckboxInput,
    moduleName: QuestionModule.Input,
    label: "多选输入",
    icon: "☑️",
    preview: () => "多选输入",
    render: ({ props }: { props: CheckboxInputQuestionProps }) => (
      <div className="pointer-events-none">
        {props.options?.map((option: OptionItem, index: number) => (
          <div key={index} className="flex items-center space-x-2">
            <input type="checkbox" id={`checkbox-${index}`} />
            <Label htmlFor={`checkbox-${index}`}>{option.label}</Label>
          </div>
        ))}
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "多选输入标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      {
        name: "options",
        label: "选项列表",
        type: "options",
        defaultValue: [
          { label: "选项1", value: genUUID() },
          { label: "选项2", value: genUUID() },
        ],
      },
    ],
  },

  // 选择题
  [QuestionType.Radio]: {
    key: QuestionType.Radio,
    moduleName: QuestionModule.Choice,
    label: "单选题",
    icon: "🔘",
    preview: () => "单选题",
    render: ({ props }: { props: RadioQuestionProps }) => (
      <div className="pointer-events-none">
        <RadioGroup value={props.options?.[0]?.value || ""}>
          {props.options?.map((option: OptionItem, index: number) => (
            <div key={index} className="flex items-center space-x-2">
              <RadioGroupItem value={option.value} id={`radio-${index}`} />
              <Label htmlFor={`radio-${index}`}>{option.label}</Label>
            </div>
          ))}
        </RadioGroup>
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "单选题标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      {
        name: "options",
        label: "选项列表",
        type: "options",
        defaultValue: [
          { label: "选项1", value: genUUID() },
          { label: "选项2", value: genUUID() },
        ],
      },
    ],
  },

  [QuestionType.Checkbox]: {
    key: QuestionType.Checkbox,
    moduleName: QuestionModule.Choice,
    label: "多选题",
    icon: "☑️",
    preview: () => "多选题",
    render: ({ props }: { props: CheckboxQuestionProps }) => (
      <div className="pointer-events-none">
        {props.options?.map((option: OptionItem, index: number) => (
          <div key={index} className="flex items-center space-x-2">
            <input type="checkbox" id={`checkbox-${index}`} />
            <Label htmlFor={`checkbox-${index}`}>{option.label}</Label>
          </div>
        ))}
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "多选题标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      {
        name: "options",
        label: "选项列表",
        type: "options",
        defaultValue: [
          { label: "选项1", value: genUUID() },
          { label: "选项2", value: genUUID() },
        ],
      },
    ],
  },

  [QuestionType.Select]: {
    key: QuestionType.Select,
    moduleName: QuestionModule.Choice,
    label: "下拉选择",
    icon: "📋",
    preview: () => "下拉选择",
    render: ({ props }: { props: SelectQuestionProps }) => (
      <div className="pointer-events-none">
        <Select value={props.options?.[0]?.value || ""}>
          <SelectTrigger>
            <SelectValue placeholder="请选择" />
          </SelectTrigger>
          <SelectContent>
            {props.options?.map((option: OptionItem, index: number) => (
              <SelectItem key={index} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "下拉选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      {
        name: "options",
        label: "选项列表",
        type: "options",
        defaultValue: [
          { label: "选项1", value: genUUID() },
          { label: "选项2", value: genUUID() },
        ],
      },
    ],
  },

  [QuestionType.Slider]: {
    key: QuestionType.Slider,
    moduleName: QuestionModule.Choice,
    label: "拖拽选择",
    icon: "🎚️",
    preview: () => "拖拽选择",
    render: ({ props }: { props: SliderQuestionProps }) => (
      <div className="pointer-events-none">
        <input
          type="range"
          min={props.min || 0}
          max={props.max || 100}
          value={props.defaultValue || 50}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground mt-1">
          <span>{props.min || 0}</span>
          <span>{props.max || 100}</span>
        </div>
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "拖拽选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "min", label: "最小值", type: "number", defaultValue: 0 },
      { name: "max", label: "最大值", type: "number", defaultValue: 100 },
      {
        name: "defaultValue",
        label: "默认值",
        type: "number",
        defaultValue: 50,
      },
    ],
  },

  // 日期类
  [QuestionType.Date]: {
    key: QuestionType.Date,
    moduleName: QuestionModule.Date,
    label: "日期选择",
    icon: "📅",
    preview: () => "日期选择",
    render: ({ props }: { props: DateQuestionProps }) => (
      <div className="pointer-events-none">
        <Input type="date" className="pointer-events-none" />
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "日期选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "min", label: "最小日期", type: "date" },
      { name: "max", label: "最大日期", type: "date" },
    ],
  },

  [QuestionType.DateTime]: {
    key: QuestionType.DateTime,
    moduleName: QuestionModule.Date,
    label: "日期时间",
    icon: "🕐",
    preview: () => "日期时间",
    render: ({ props }: { props: DateTimeQuestionProps }) => (
      <div className="pointer-events-none">
        <Input type="datetime-local" className="pointer-events-none" />
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "日期时间标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "min", label: "最小日期时间", type: "datetime-local" },
      { name: "max", label: "最大日期时间", type: "datetime-local" },
    ],
  },

  [QuestionType.Time]: {
    key: QuestionType.Time,
    moduleName: QuestionModule.Date,
    label: "时间选择",
    icon: "⏰",
    preview: () => "时间选择",
    render: ({ props }: { props: TimeQuestionProps }) => (
      <div className="pointer-events-none">
        <Input type="time" className="pointer-events-none" />
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "时间选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "min", label: "最小时间", type: "time" },
      { name: "max", label: "最大时间", type: "time" },
    ],
  },

  [QuestionType.Year]: {
    key: QuestionType.Year,
    moduleName: QuestionModule.Date,
    label: "年份选择",
    icon: "📅",
    preview: () => "年份选择",
    render: ({ props }: { props: YearQuestionProps }) => (
      <div className="pointer-events-none">
        <Input
          type="number"
          placeholder="选择年份"
          className="pointer-events-none"
        />
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "年份选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "min", label: "最小年份", type: "number", defaultValue: 1900 },
      { name: "max", label: "最大年份", type: "number", defaultValue: 2100 },
    ],
  },

  [QuestionType.Month]: {
    key: QuestionType.Month,
    moduleName: QuestionModule.Date,
    label: "月份选择",
    icon: "📅",
    preview: () => "月份选择",
    render: ({ props }: { props: MonthQuestionProps }) => (
      <div className="pointer-events-none">
        <Input type="month" className="pointer-events-none" />
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "月份选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
      { name: "min", label: "最小月份", type: "month" },
      { name: "max", label: "最大月份", type: "month" },
    ],
  },

  // 区域类
  [QuestionType.Province]: {
    key: QuestionType.Province,
    moduleName: QuestionModule.Area,
    label: "省份选择",
    icon: "🗺️",
    preview: () => "省份选择",
    render: ({ props }: { props: ProvinceQuestionProps }) => (
      <div className="pointer-events-none">
        <Select value="">
          <SelectTrigger>
            <SelectValue placeholder="请选择省份" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="beijing">北京市</SelectItem>
            <SelectItem value="shanghai">上海市</SelectItem>
            <SelectItem value="guangdong">广东省</SelectItem>
            <SelectItem value="jiangsu">江苏省</SelectItem>
            <SelectItem value="zhejiang">浙江省</SelectItem>
          </SelectContent>
        </Select>
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "省份选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
    ],
  },

  [QuestionType.ProvinceCity]: {
    key: QuestionType.ProvinceCity,
    moduleName: QuestionModule.Area,
    label: "省市选择",
    icon: "🗺️",
    preview: () => "省市选择",
    render: ({ props }: { props: ProvinceCityQuestionProps }) => (
      <div className="pointer-events-none space-y-2">
        <Select value="">
          <SelectTrigger>
            <SelectValue placeholder="请选择省份" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="beijing">北京市</SelectItem>
            <SelectItem value="shanghai">上海市</SelectItem>
            <SelectItem value="guangdong">广东省</SelectItem>
          </SelectContent>
        </Select>
        <Select value="">
          <SelectTrigger>
            <SelectValue placeholder="请选择城市" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="beijing">北京市</SelectItem>
            <SelectItem value="shanghai">上海市</SelectItem>
            <SelectItem value="guangzhou">广州市</SelectItem>
            <SelectItem value="shenzhen">深圳市</SelectItem>
          </SelectContent>
        </Select>
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "省市选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
    ],
  },

  [QuestionType.Area]: {
    key: QuestionType.Area,
    moduleName: QuestionModule.Area,
    label: "省市区选择",
    icon: "🗺️",
    preview: () => "省市区选择",
    render: ({ props }: { props: AreaQuestionProps }) => (
      <div className="pointer-events-none space-y-2">
        <Select value="">
          <SelectTrigger>
            <SelectValue placeholder="请选择省份" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="beijing">北京市</SelectItem>
            <SelectItem value="shanghai">上海市</SelectItem>
            <SelectItem value="guangdong">广东省</SelectItem>
          </SelectContent>
        </Select>
        <Select value="">
          <SelectTrigger>
            <SelectValue placeholder="请选择城市" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="beijing">北京市</SelectItem>
            <SelectItem value="shanghai">上海市</SelectItem>
            <SelectItem value="guangzhou">广州市</SelectItem>
          </SelectContent>
        </Select>
        <Select value="">
          <SelectTrigger>
            <SelectValue placeholder="请选择区县" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="chaoyang">朝阳区</SelectItem>
            <SelectItem value="haidian">海淀区</SelectItem>
            <SelectItem value="tianhe">天河区</SelectItem>
          </SelectContent>
        </Select>
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "省市区选择标题",
      },
      { name: "code", label: "编码", type: "input", defaultValue: "" },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
    ],
  },

  [QuestionType.SEX]: {
    key: QuestionType.SEX,
    moduleName: QuestionModule.Special,
    label: "性别",
    icon: <SexIcon />,
    preview: () => "性别",
    render: ({ props }: { props: SexQuestionProps }) => (
      <div className="pointer-events-none select-none">
        <div className="mb-2 font-medium">请选择性别：</div>
        <div className="flex gap-3">
          <div className="flex flex-col items-center">
            <span className="text-2xl">♂️</span>
            <span className="text-xs mt-1">男</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-2xl">♀️</span>
            <span className="text-xs mt-1">女</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-2xl">❓</span>
            <span className="text-xs mt-1">保密</span>
          </div>
        </div>
      </div>
    ),
    props: [
      {
        name: "title",
        label: "题目标题",
        type: "input",
        defaultValue: "姓名",
        disabled: true,
      },
      {
        name: "code",
        label: "编码",
        type: "input",
        defaultValue: "sex",
        disabled: true,
      },
      {
        name: "required",
        label: "是否必填",
        type: "Switch",
        defaultValue: false,
      },
    ],
  },
}; 
