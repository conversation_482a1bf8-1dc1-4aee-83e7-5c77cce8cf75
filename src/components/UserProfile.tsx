'use client';

import { useUserStore } from '@/store/user';
import { useRouter } from 'next/navigation';

export function UserProfile() {
  const { user, isLoading, fetchCurrentUser, logout } = useUserStore();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      // 退出成功后重定向到登录页面
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      // 即使退出失败，也重定向到登录页面
      router.push('/login');
    }
  };

  const handleRefreshUser = async () => {
    try {
      // 强制刷新用户信息
      await fetchCurrentUser(true);
    } catch (error) {
      console.error('Failed to refresh user info:', error);
    }
  };

  if (isLoading) {
    return <div>加载用户信息中...</div>;
  }

  if (!user) {
    return <div>未登录</div>;
  }

  return (
    <div className="p-4 border border-border rounded-lg bg-card">
      <h2 className="text-xl font-bold mb-4 text-card-foreground">用户信息</h2>
      <div className="space-y-2 text-card-foreground">
        <p><strong>用户名:</strong> {user.username}</p>
        <p><strong>真实姓名:</strong> {user.realName || '未设置'}</p>
        <p><strong>邮箱:</strong> {user.email || '未设置'}</p>
        <p><strong>手机号:</strong> {user.phone || '未设置'}</p>
        <p><strong>状态:</strong> {user.status === 1 ? '正常' : '禁用'}</p>
        <p><strong>角色:</strong> {user.roles?.map(role => role.name).join(', ') || '无'}</p>
        <p><strong>最后登录:</strong> {user.lastLoginTime || '从未登录'}</p>
      </div>
      
      <div className="mt-4 flex gap-2">
        <button 
          onClick={handleRefreshUser}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
        >
          刷新用户信息
        </button>
        
        <button 
          onClick={handleLogout}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700"
        >
          退出登录
        </button>
      </div>
    </div>
  );
} 