"use client";

import { useEffect, useRef } from "react";
import { useUserStore } from "@/store/user";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

interface UserProviderProps {
  children: React.ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const { initialize, isInitialized } = useUserStore();
  
  // 使用useRef确保QueryClient实例在组件重新渲染时保持不变
  const queryClientRef = useRef<QueryClient | null>(null);
  
  if (!queryClientRef.current) {
    queryClientRef.current = new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
          gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
          retry: 1, // 失败时重试1次
          refetchOnWindowFocus: false, // 窗口聚焦时不重新获取
          refetchOnReconnect: true, // 网络重连时重新获取
        },
        mutations: {
          retry: 1, // 失败时重试1次
        },
      },
    });
  }

  useEffect(() => {
    // 在组件挂载时初始化用户状态
    initialize();
  }, [initialize]);

  // 如果还没有初始化完成，显示加载状态
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">正在初始化...</p>
        </div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClientRef.current}>
      {children}
    </QueryClientProvider>
  );
}
 