"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { CreateQuestionnaireRequest, UpdateQuestionnaireRequest } from "@/lib/api/questionnaire";
import { QuestionnaireType, QuestionnaireStatus, QuestionnaireResponseDTO } from "@/types/questionnaire";

interface QuestionnaireFormProps<T> {
  mode: 'create' | 'edit';
  initialData?: QuestionnaireResponseDTO;
  onSubmit: (data: T) => void;
  onCancel?: () => void;
}

export function QuestionnaireForm<T extends CreateQuestionnaireRequest | UpdateQuestionnaireRequest>({ 
  mode, 
  initialData, 
  onSubmit, 
  onCancel 
}: QuestionnaireFormProps<T>) {
  const [formData, setFormData] = useState<T>({
    title: "",
    type: QuestionnaireType.Questionnaire,
    description: "",
    status: QuestionnaireStatus.Draft,
    isAnonymous: false,
    allowMultiple: false,
  } as T);

  // 如果是编辑模式且有初始数据，则填充表单
  useEffect(() => {
    if (mode === 'edit' && initialData) {
      setFormData({
        title: initialData.title,
        description: initialData.description || "",
        status: initialData.status,
        startTime: initialData.startTime || "",
        endTime: initialData.endTime || "",
        isAnonymous: initialData.isAnonymous,
        allowMultiple: initialData.allowMultiple,
        maxSubmissions: initialData.maxSubmissions,
      } as T);
    }
  }, [mode, initialData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const isCreateMode = mode === 'create';

  return (
    <form onSubmit={handleSubmit} className="space-y-6 mt-6 px-4">
      <div className="space-y-2">
        <Label htmlFor="title">问卷标题 *</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          placeholder="请输入问卷标题"
          required
        />
      </div>

      {/* 只在创建模式下显示类型选择 */}
      {isCreateMode && (
        <div className="space-y-2">
          <Label>问卷类型 *</Label>
          <RadioGroup
            value={(formData as CreateQuestionnaireRequest).type?.toString()}
            onValueChange={(value) => setFormData({ 
              ...formData, 
              type: Number(value) as QuestionnaireType 
            } as T)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={QuestionnaireType.Questionnaire.toString()} id="questionnaire" />
              <Label htmlFor="questionnaire">问卷</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={QuestionnaireType.Form.toString()} id="form" />
              <Label htmlFor="form">表单</Label>
            </div>
          </RadioGroup>
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="description">问卷描述</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="请输入问卷描述"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label>状态</Label>
        <RadioGroup
          value={formData.status?.toString()}
          onValueChange={(value) => setFormData({ ...formData, status: Number(value) as QuestionnaireStatus })}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value={QuestionnaireStatus.Draft.toString()} id="draft" />
            <Label htmlFor="draft">草稿</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value={QuestionnaireStatus.Published.toString()} id="published" />
            <Label htmlFor="published">发布</Label>
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-2">
        <Label htmlFor="startTime">开始时间</Label>
        <Input
          id="startTime"
          type="datetime-local"
          value={formData.startTime || ""}
          onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="endTime">结束时间</Label>
        <Input
          id="endTime"
          type="datetime-local"
          value={formData.endTime || ""}
          onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
        />
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="isAnonymous">是否匿名</Label>
        <Switch
          id="isAnonymous"
          checked={formData.isAnonymous}
          onCheckedChange={(checked) => setFormData({ ...formData, isAnonymous: checked })}
        />
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="allowMultiple">允许多次提交</Label>
        <Switch
          id="allowMultiple"
          checked={formData.allowMultiple}
          onCheckedChange={(checked) => setFormData({ ...formData, allowMultiple: checked })}
        />
      </div>

      {formData.allowMultiple && (
        <div className="space-y-2">
          <Label htmlFor="maxSubmissions">最大提交次数</Label>
          <Input
            id="maxSubmissions"
            type="number"
            value={formData.maxSubmissions || ""}
            onChange={(e) => setFormData({ ...formData, maxSubmissions: Number(e.target.value) })}
            placeholder="请输入最大提交次数"
          />
        </div>
      )}

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
        )}
        <Button type="submit" disabled={!formData.title}>
          {isCreateMode ? '创建问卷' : '更新问卷'}
        </Button>
      </div>
    </form>
  );
}

// 为了保持向后兼容，保留原来的组件名
export function CreateQuestionnaireForm({ onSubmit }: { onSubmit: (data: CreateQuestionnaireRequest) => void }) {
  return <QuestionnaireForm<CreateQuestionnaireRequest> mode="create" onSubmit={onSubmit} />;
} 