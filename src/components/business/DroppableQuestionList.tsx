import { useDrop } from 'react-dnd';
import type { DropTargetMonitor } from 'react-dnd';
import { ItemTypes, DragItem } from './dnd-types';
import { QuestionResponseDTO, QuestionType } from '@/types/questionnaire';
import { useRef, useState } from 'react';
import { DraggableQuestion } from './DraggableQuestion';

interface DroppableQuestionListProps {
  questions: QuestionResponseDTO[];
  selectedIndex: number | null;
  onQuestionSelect: (index: number) => void;
  onQuestionDelete: (index: number) => void;
  onQuestionMove: (fromIndex: number, toIndex: number) => void;
  onQuestionAdd: (type: QuestionType, index: number) => void;
}

export function DroppableQuestionList({
  questions,
  selectedIndex,
  onQuestionSelect,
  onQuestionDelete,
  onQuestionMove,
  onQuestionAdd,
}: DroppableQuestionListProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [showInsertLine, setShowInsertLine] = useState(false);

  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.QUESTION, ItemTypes.NEW_QUESTION],
    hover: (item: DragItem, monitor) => {
      if (!ref.current) return;

      // 处理新题目拖拽到空容器
      if (monitor.getItemType() === ItemTypes.NEW_QUESTION && questions.length === 0) {
        setShowInsertLine(true);
      }

      // 处理新题目拖拽到有题目的容器底部
      if (monitor.getItemType() === ItemTypes.NEW_QUESTION && questions.length > 0) {
        const hoverBoundingRect = ref.current?.getBoundingClientRect();
        if (!hoverBoundingRect) return;

        // Get mouse position
        const clientOffset = monitor.getClientOffset();
        if (!clientOffset) return;

        // Get pixels to the bottom
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;
        const containerHeight = hoverBoundingRect.bottom - hoverBoundingRect.top;

        // 如果鼠标在容器底部区域（最后20%的区域），显示插入线
        if (hoverClientY > containerHeight * 0.8) {
          setShowInsertLine(true);
        } else {
          setShowInsertLine(false);
        }
      }
    },
    drop: (item: DragItem, monitor) => {
      if (!monitor.isOver({ shallow: true })) return;
      
      if (monitor.getItemType() === ItemTypes.NEW_QUESTION) {
        // 如果是空列表，直接添加到开头
        if (questions.length === 0) {
          onQuestionAdd(item.type as QuestionType, 0);
          setShowInsertLine(false);
        } else {
          // 如果有题目，添加到最后一个位置
          onQuestionAdd(item.type as QuestionType, questions.length);
          setShowInsertLine(false);
        }
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    })
  });

  // 当不再悬停时重置插入线
  if (!isOver && showInsertLine) {
    setShowInsertLine(false);
  }

  drop(ref);

  return (
    <div
      ref={ref}
      className={`flex flex-col gap-4 min-h-[400px] relative ${isOver ? 'bg-accent/20' : ''}`}
    >
      {questions.map((question, index) => (
        <DraggableQuestion
          key={question.id}
          question={question}
          index={index}
          isSelected={selectedIndex === index}
          onSelect={() => onQuestionSelect(index)}
          onDelete={() => onQuestionDelete(index)}
          onMove={onQuestionMove}
          onQuestionAdd={onQuestionAdd}
        />
      ))}
      
      {/* 空状态或底部插入区域 */}
      {questions.length === 0 ? (
        <div className="flex flex-1 items-center justify-center text-muted-foreground h-40 select-none relative">
          {showInsertLine && (
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-blue-500 dark:bg-blue-400 rounded-full z-10 transform -translate-y-1/2 shadow-lg animate-pulse" />
          )}
          <span className="relative z-20 bg-card px-4 py-2 rounded-lg border border-border">
            拖拽左侧题型到此处以添加题目
          </span>
        </div>
      ) : (
        /* 有题目时的底部插入区域 */
        <div className="flex-1 min-h-[100px] relative">
          {showInsertLine && (
            <div className="absolute top-0 left-0 right-0 h-0.5 bg-blue-500 dark:bg-blue-400 rounded-full z-10 shadow-lg animate-pulse" />
          )}
          <div className="flex items-center justify-center text-muted-foreground h-full select-none">
            <span className="bg-card px-4 py-2 rounded-lg border border-border">
              拖拽到此处添加题目到末尾
            </span>
          </div>
        </div>
      )}
    </div>
  );
} 