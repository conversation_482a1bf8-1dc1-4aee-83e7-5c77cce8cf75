import { useDrag } from 'react-dnd';
import { QuestionType } from '@/types/questionnaire';
import { QuestionTypeRegistry } from '@/config/question-type-registry';
import { ItemTypes } from './dnd-types';

interface DraggableQuestionTypeProps {
  type: keyof typeof QuestionTypeRegistry;
}

export function DraggableQuestionType({ type }: DraggableQuestionTypeProps) {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.NEW_QUESTION,
    item: { type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const registryItem = QuestionTypeRegistry[type];

  if (!registryItem) return null;

  return (
    <div
      ref={drag as any}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className="flex items-center gap-3 px-3 py-2 rounded-xl cursor-grab hover:bg-accent active:bg-accent/80 transition select-none"
    >
      <span className="text-lg">{registryItem.icon}</span>
      <span className="font-medium text-foreground">{registryItem.label}</span>
    </div>
  );
} 