import React, { useRef, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useUserStore } from "@/store/user";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { questionnaireApi, LlmRequest } from "@/lib/api/questionnaire";

interface Message {
  role: "user" | "ai";
  content: string;
}

interface AiChatDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// 任务状态枚举
enum TaskStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED"
}

// 任务响应接口
interface TaskResponse {
  taskId: string;
  status: TaskStatus;
  message?: string;
}

// SSE 事件数据接口
interface SSEEventData {
  taskId: string;
  status: TaskStatus;
  content?: string;
  error?: string;
}

const PRESET_QUESTIONS = [
  {
    key: "theme",
    text: "问卷主题是什么？（例如：用户满意度调查）",
  },
  { key: "questionCount", text: "希望题目大概有多少数量？（例如：10）" },
  {
    key: "questionTypes",
    text: "希望主要包含哪些题型？（例如：单选题, 多选题, 文本题）",
  },
  {
    key: "scenario",
    text: "问卷的应用场景是什么？（例如：用于收集产品上线后的用户反馈）",
  },
  {
    key: "description",
    text: "还有其他描述或要求吗？（例如：希望问题能覆盖产品的易用性、功能性和稳定性等方面）",
  },
];

export const AiChatDialog: React.FC<AiChatDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isFlowFinished, setIsFlowFinished] = useState(false);
  const [isStreamFinished, setIsStreamFinished] = useState(false);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);

  // 新增异步任务相关状态
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [taskStatus, setTaskStatus] = useState<TaskStatus | null>(null);

  const abortRef = useRef<AbortController | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const sseRef = useRef<EventSource | null>(null);

  const user = useUserStore((state) => state.user);
  const displayAvatar = user?.avatar || "";
  const displayName = user?.realName || user?.username || "用户";

  const queryClient = useQueryClient();

  // SSE 连接管理
  const connectSSE = (taskId: string) => {
    // 清理之前的连接
    if (sseRef.current) {
      sseRef.current.close();
    }

    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "";
    const token = localStorage.getItem("access_token") || "";
    // 由于 EventSource 不支持自定义 headers，通过 URL 参数传递 token
    const url = `${baseUrl}/api/subscribe/${taskId}?token=${encodeURIComponent(token)}`;

    const eventSource = new EventSource(url);

    eventSource.onopen = () => {
      console.log("SSE 连接已建立");
      setTaskStatus(TaskStatus.PROCESSING);
    };

    eventSource.onmessage = (event) => {
      try {
        const data: SSEEventData = JSON.parse(event.data);
        console.log("收到 SSE 消息:", data);

        setTaskStatus(data.status);

        if (data.status === TaskStatus.COMPLETED && data.content) {
          // 任务完成，更新 AI 消息内容
          setMessages((prev) => {
            const newMessages = [...prev];
            const lastMessage = newMessages[newMessages.length - 1];
            if (lastMessage && lastMessage.role === "ai") {
              lastMessage.content = data.content!;
            }
            return newMessages;
          });
          setIsStreamFinished(true);
          setLoading(false);
          toast.success("AI 生成完成");
        } else if (data.status === TaskStatus.FAILED) {
          // 任务失败
          setMessages((prev) => [
            ...prev.slice(0, -1),
            { role: "ai", content: `生成失败: ${data.error || "未知错误"}` }
          ]);
          setLoading(false);
          toast.error("AI 生成失败");
        }
      } catch (error) {
        console.error("解析 SSE 消息失败:", error);
      }
    };

    eventSource.onerror = (error) => {
      console.error("SSE 连接错误:", error);
      setTaskStatus(TaskStatus.FAILED);
      setLoading(false);
      toast.error("连接中断，请重试");
    };

    sseRef.current = eventSource;
  };

  // 清理 SSE 连接
  const cleanupSSE = () => {
    if (sseRef.current) {
      sseRef.current.close();
      sseRef.current = null;
    }
  };

  useEffect(() => {
    if (open) {
      setMessages([{ role: "ai", content: PRESET_QUESTIONS[0].text }]);
      setAnswers({});
      setCurrentQuestionIndex(0);
      setIsFlowFinished(false);
      setIsStreamFinished(false);
      setInput("");
      setLoading(false);
      setGenerating(false);
      setCurrentTaskId(null);
      setTaskStatus(null);
    } else {
      // 对话框关闭时清理 SSE 连接
      cleanupSSE();
    }
  }, [open]);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      cleanupSSE();
      if (abortRef.current) {
        abortRef.current.abort();
      }
    };
  }, []);

  const scrollToBottom = () => {
    setTimeout(() => {
      scrollRef.current?.scrollTo({
        top: scrollRef.current.scrollHeight,
        behavior: "smooth",
      });
    }, 100);
  };

  const triggerAsyncGeneration = async (
    finalAnswers: Record<string, string>
  ) => {
    setLoading(true);

    // 添加一个占位的 AI 消息
    const aiMsg: Message = { role: "ai", content: "正在生成问卷，请稍候..." };
    setMessages((prev) => [...prev, aiMsg]);
    scrollToBottom();

    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "";
      const url = `${baseUrl}/questionnaires/generate`;
      const token = localStorage.getItem("access_token") || "";

      const payload = {
        ...finalAnswers,
        questionCount: parseInt(finalAnswers.questionCount, 10) || 10,
      };

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Qs-Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: TaskResponse = await response.json();

      if (result.taskId) {
        setCurrentTaskId(result.taskId);
        setTaskStatus(TaskStatus.PENDING);

        // 建立 SSE 连接监听任务状态
        connectSSE(result.taskId);

        toast.success("任务已提交，正在生成中...");
      } else {
        throw new Error("未收到任务ID");
      }
    } catch (error) {
      console.error("提交生成任务失败:", error);
      setMessages((prev) => [
        ...prev.slice(0, -1),
        { role: "ai", content: `生成失败: ${(error as Error).message}` }
      ]);
      setLoading(false);
      toast.error("提交任务失败");
    }
  };

  const handleSend = async () => {
    if (!input.trim() || loading || isFlowFinished) return;
    const userMsg: Message = { role: "user", content: input };
    const currentKey = PRESET_QUESTIONS[currentQuestionIndex].key;
    const newAnswers = { ...answers, [currentKey]: input };

    setMessages((prev) => [...prev, userMsg]);
    setAnswers(newAnswers);
    setInput("");
    setLoading(true);
    scrollToBottom();

    await new Promise((res) => setTimeout(res, 300));

    const nextIndex = currentQuestionIndex + 1;
    if (nextIndex < PRESET_QUESTIONS.length) {
      setMessages((prev) => [
        ...prev,
        { role: "ai", content: PRESET_QUESTIONS[nextIndex].text },
      ]);
      setCurrentQuestionIndex(nextIndex);
      setLoading(false);
    } else {
      setIsFlowFinished(true);
      triggerAsyncGeneration(newAnswers);
    }
    scrollToBottom();
  };

  const handleGenerate = async () => {
    if (!isStreamFinished || messages.length === 0) return;
    const lastMsg = messages[messages.length - 1];
    if (lastMsg.role !== 'ai' || !lastMsg.content) return;

    try {
      setGenerating(true);
      const req: LlmRequest = { content: lastMsg.content };
      const result = await questionnaireApi.dataFormatAnalyze(req);
      toast.success(result ? "问卷创建成功" : "问卷创建失败");
      if (result) {
        // 清理 SSE 连接
        cleanupSSE();
        onOpenChange(false);
        queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
      }
    } catch (e) {
      toast.error("问卷创建失败");
    } finally {
      setGenerating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };
  
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // 清理所有连接
      if (abortRef.current) {
        abortRef.current.abort();
      }
      cleanupSSE();
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="!max-w-6xl w-full h-[80vh] flex flex-col">
        {(generating || (taskStatus && taskStatus !== TaskStatus.COMPLETED && taskStatus !== TaskStatus.FAILED)) && (
          <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/40">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
              <div className="text-white text-lg">
                {generating ? "正在创建问卷..." :
                 taskStatus === TaskStatus.PENDING ? "任务排队中..." :
                 taskStatus === TaskStatus.PROCESSING ? "AI 正在生成..." :
                 "处理中..."}
              </div>
              {currentTaskId && (
                <div className="text-white text-sm mt-2 opacity-75">
                  任务ID: {currentTaskId}
                </div>
              )}
            </div>
          </div>
        )}
        <DialogHeader>
          <DialogTitle>AI智能问卷助手</DialogTitle>
        </DialogHeader>
        <div
          ref={scrollRef}
          className="flex-1 overflow-y-auto bg-muted rounded p-3 mb-2 border"
        >
          {messages.map((msg, idx) => (
            <div
              key={idx}
              className={`mb-2 flex ${
                msg.role === "user" ? "justify-end" : "justify-start"
              } items-start`}
            >
              {msg.role === "ai" && (
                <div className="mr-2 flex-shrink-0">
                  <Avatar className="h-8 w-8 rounded-lg bg-white border">
                    <span className="text-xl flex items-center justify-center w-full h-full">
                      🤖
                    </span>
                  </Avatar>
                </div>
              )}
              <div
                className={`px-3 py-2 rounded-lg max-w-[80%] whitespace-pre-wrap text-sm ${
                  msg.role === "user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-white border"
                }`}
              >
                {msg.content}
                {msg.role === "ai" &&
                  idx === messages.length - 1 &&
                  loading && (
                    <span className="inline-block align-middle ml-2">
                      <span className="animate-bounce">.</span>
                      <span className="animate-bounce delay-150">.</span>
                      <span className="animate-bounce delay-300">.</span>
                    </span>
                  )}
              </div>
              {msg.role === "user" && (
                <div className="ml-2 flex-shrink-0">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={displayAvatar} alt={displayName} />
                    <AvatarFallback className="rounded-lg">U</AvatarFallback>
                  </Avatar>
                </div>
              )}
            </div>
          ))}
        </div>
        <DialogFooter className="gap-2">
          <Button
            variant="secondary"
            onClick={handleGenerate}
            disabled={!isStreamFinished || generating || (taskStatus !== null && taskStatus !== TaskStatus.COMPLETED)}
          >
            {taskStatus === TaskStatus.COMPLETED ? "创建问卷" : "生成"}
          </Button>
          <Input
            placeholder="请输入你的需求..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={loading || isFlowFinished || generating}
            className="flex-1"
            autoFocus
          />
          <Button
            onClick={handleSend}
            disabled={loading || isFlowFinished || !input.trim()}
          >
            发送
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

 