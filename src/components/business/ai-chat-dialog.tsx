import React, { useRef, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useUserStore } from "@/store/user";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { questionnaireApi, LlmRequest } from "@/lib/api/questionnaire";
import { useAITaskStore, TaskStatus } from "@/store/ai-task";

interface Message {
  role: "user" | "ai";
  content: string;
}

interface AiChatDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}



const PRESET_QUESTIONS = [
  {
    key: "theme",
    text: "问卷主题是什么？（例如：用户满意度调查）",
  },
  { key: "questionCount", text: "希望题目大概有多少数量？（例如：10）" },
  {
    key: "questionTypes",
    text: "希望主要包含哪些题型？（例如：单选题, 多选题, 文本题）",
  },
  {
    key: "scenario",
    text: "问卷的应用场景是什么？（例如：用于收集产品上线后的用户反馈）",
  },
  {
    key: "description",
    text: "还有其他描述或要求吗？（例如：希望问题能覆盖产品的易用性、功能性和稳定性等方面）",
  },
];

export const AiChatDialog: React.FC<AiChatDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isFlowFinished, setIsFlowFinished] = useState(false);
  const [isStreamFinished, setIsStreamFinished] = useState(false);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);

  // 新增异步任务相关状态
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);

  const abortRef = useRef<AbortController | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const user = useUserStore((state) => state.user);
  const displayAvatar = user?.avatar || "";
  const displayName = user?.realName || user?.username || "用户";

  const queryClient = useQueryClient();

  // 使用全局任务管理
  const { addTask, connectSSE, disconnectSSE, getTask } = useAITaskStore();

  // 获取当前任务状态
  const currentTask = currentTaskId ? getTask(currentTaskId) : null;
  const taskStatus = currentTask?.status || null;



  useEffect(() => {
    if (open) {
      setMessages([{ role: "ai", content: PRESET_QUESTIONS[0].text }]);
      setAnswers({});
      setCurrentQuestionIndex(0);
      setIsFlowFinished(false);
      setIsStreamFinished(false);
      setInput("");
      setLoading(false);
      setGenerating(false);
      setCurrentTaskId(null);

      // 确保对话框打开时输入框获得焦点
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 监听当前任务状态变化，更新 UI
  useEffect(() => {
    if (currentTask) {
      if (currentTask.status === TaskStatus.COMPLETED) {
        // 任务完成
        setGenerating(false);
        toast.success("问卷创建完成！");

        // 自动关闭对话框并刷新问卷列表
        setTimeout(() => {
          disconnectSSE(currentTask.taskId);
          onOpenChange(false);
          queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
        }, 1500);

      } else if (currentTask.status === TaskStatus.FAILED) {
        // 任务失败
        setGenerating(false);
        toast.error(`问卷创建失败: ${currentTask.error || "未知错误"}`);

        // 失败后重新聚焦输入框，让用户可以重试
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      }
    }
  }, [currentTask, disconnectSSE, onOpenChange, queryClient]);

  const scrollToBottom = () => {
    setTimeout(() => {
      scrollRef.current?.scrollTo({
        top: scrollRef.current.scrollHeight,
        behavior: "smooth",
      });
    }, 100);
  };

  const triggerStreamGeneration = async (
    finalAnswers: Record<string, string>
  ) => {
    setLoading(true);
    const controller = new AbortController();
    abortRef.current = controller;

    const aiMsg: Message = { role: "ai", content: "" };
    setMessages((prev) => [...prev, aiMsg]);
    scrollToBottom();

    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "";
      const url = `${baseUrl}/questionnaires/generate`;
      const token = localStorage.getItem("access_token") || "";

      const payload = {
        ...finalAnswers,
        questionCount: parseInt(finalAnswers.questionCount, 10) || 10,
      };

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Qs-Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      if (!response.body) throw new Error("无响应体");
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          setIsStreamFinished(true);
          // 流式生成完成后，重新聚焦输入框
          setTimeout(() => {
            inputRef.current?.focus();
          }, 100);
          break;
        }
        buffer += decoder.decode(value, { stream: true });
        const parts = buffer.split("\n\n");
        for (let i = 0; i < parts.length - 1; i++) {
          const line = parts[i];
          if (line.startsWith("data:")) {
            try {
              const json = JSON.parse(line.substring(5));
              if (json.content) {
                aiMsg.content += json.content;
                setMessages((prev) => [...prev.slice(0, -1), { ...aiMsg }]);
                scrollToBottom();
              }
            } catch (e) {
              console.error("SSE parsing error:", e);
            }
          }
        }
        buffer = parts[parts.length - 1];
      }
    } catch (e) {
      if ((e as Error).name !== 'AbortError') {
        setMessages((prev) => [...prev, { role: "ai", content: "[AI回复失败]" }]);
      }
    } finally {
      setLoading(false);
      abortRef.current = null;
      scrollToBottom();
      // 确保在加载完成后重新聚焦输入框
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  const handleSend = async () => {
    if (!input.trim() || loading || isFlowFinished) return;
    const userMsg: Message = { role: "user", content: input };
    const currentKey = PRESET_QUESTIONS[currentQuestionIndex].key;
    const newAnswers = { ...answers, [currentKey]: input };

    setMessages((prev) => [...prev, userMsg]);
    setAnswers(newAnswers);
    setInput("");
    setLoading(true);
    scrollToBottom();

    // 重新聚焦到输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);

    await new Promise((res) => setTimeout(res, 300));

    const nextIndex = currentQuestionIndex + 1;
    if (nextIndex < PRESET_QUESTIONS.length) {
      setMessages((prev) => [
        ...prev,
        { role: "ai", content: PRESET_QUESTIONS[nextIndex].text },
      ]);
      setCurrentQuestionIndex(nextIndex);
      setLoading(false);
      // 再次确保输入框聚焦
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } else {
      setIsFlowFinished(true);
      triggerStreamGeneration(newAnswers);
    }
    scrollToBottom();
  };

  const handleGenerate = async () => {
    if (!isStreamFinished || messages.length === 0) return;
    const lastMsg = messages[messages.length - 1];
    if (lastMsg.role !== 'ai' || !lastMsg.content) return;

    try {
      setGenerating(true);
      const req: LlmRequest = { content: lastMsg.content };

      // 调用 dataFormatAnalyze，这个接口现在返回 taskId
      const result = await questionnaireApi.dataFormatAnalyze(req);

      // result 现在是 FormatTaskCreateResponse 类型，包含 taskId
      setCurrentTaskId(result.taskId);

      // 添加任务到全局状态并建立 SSE 连接
      addTask(result.taskId);
      connectSSE(result.taskId);

      toast.success("问卷创建任务已提交，正在处理中...");

      // 不关闭对话框，让用户看到进度
    } catch (e) {
      console.error("问卷创建失败:", e);
      toast.error("问卷创建失败");
    } finally {
      setGenerating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };
  
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // 如果有正在进行的任务，提示用户但允许关闭（任务在后台继续）
      if (currentTaskId && taskStatus && taskStatus !== TaskStatus.COMPLETED && taskStatus !== TaskStatus.FAILED) {
        toast.info("任务将在后台继续进行，完成后会有通知");
      }

      // 清理本地状态
      if (abortRef.current) {
        abortRef.current.abort();
      }
      // 注意：不清理 SSE 连接，让任务在后台继续
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="!max-w-6xl w-full h-[80vh] flex flex-col">
        {(generating || (taskStatus && taskStatus !== TaskStatus.COMPLETED && taskStatus !== TaskStatus.FAILED)) && (
          <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/40">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
              <div className="text-white text-lg">
                {generating && !currentTaskId ? "正在提交任务..." :
                 generating && currentTaskId ? "正在创建问卷..." :
                 taskStatus === TaskStatus.PENDING ? "任务排队中..." :
                 taskStatus === TaskStatus.PROCESSING ? "正在创建问卷..." :
                 "处理中..."}
              </div>
              {currentTaskId && (
                <div className="text-white text-sm mt-2 opacity-75">
                  任务ID: {currentTaskId}
                </div>
              )}
            </div>
          </div>
        )}
        <DialogHeader>
          <DialogTitle>AI智能问卷助手</DialogTitle>
        </DialogHeader>
        <div
          ref={scrollRef}
          className="flex-1 overflow-y-auto bg-muted rounded p-3 mb-2 border"
        >
          {messages.map((msg, idx) => (
            <div
              key={idx}
              className={`mb-2 flex ${
                msg.role === "user" ? "justify-end" : "justify-start"
              } items-start`}
            >
              {msg.role === "ai" && (
                <div className="mr-2 flex-shrink-0">
                  <Avatar className="h-8 w-8 rounded-lg bg-white border">
                    <span className="text-xl flex items-center justify-center w-full h-full">
                      🤖
                    </span>
                  </Avatar>
                </div>
              )}
              <div
                className={`px-3 py-2 rounded-lg max-w-[80%] whitespace-pre-wrap text-sm ${
                  msg.role === "user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-white border"
                }`}
              >
                {msg.content}
                {msg.role === "ai" &&
                  idx === messages.length - 1 &&
                  loading && (
                    <span className="inline-block align-middle ml-2">
                      <span className="animate-bounce">.</span>
                      <span className="animate-bounce delay-150">.</span>
                      <span className="animate-bounce delay-300">.</span>
                    </span>
                  )}
              </div>
              {msg.role === "user" && (
                <div className="ml-2 flex-shrink-0">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={displayAvatar} alt={displayName} />
                    <AvatarFallback className="rounded-lg">U</AvatarFallback>
                  </Avatar>
                </div>
              )}
            </div>
          ))}
        </div>
        <DialogFooter className="gap-2">
          <Button
            variant="secondary"
            onClick={handleGenerate}
            disabled={!isStreamFinished || generating || (taskStatus !== null && taskStatus !== TaskStatus.COMPLETED && taskStatus !== TaskStatus.FAILED)}
          >
            {generating ? "创建中..." :
             taskStatus === TaskStatus.PROCESSING ? "创建中..." :
             taskStatus === TaskStatus.PENDING ? "排队中..." :
             "创建问卷"}
          </Button>
          <Input
            ref={inputRef}
            placeholder="请输入你的需求..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={loading || isFlowFinished || generating}
            className="flex-1"
            autoFocus
          />
          <Button
            onClick={handleSend}
            disabled={loading || isFlowFinished || !input.trim()}
          >
            发送
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

 