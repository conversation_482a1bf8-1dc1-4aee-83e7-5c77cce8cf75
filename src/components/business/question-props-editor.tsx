"use client";

import React from "react";
import {
  QuestionResponseDTO,
  QuestionProps,
  QuestionType,
  OptionItem,
  QuestionLogicExpression,
  QuestionnaireResponseDTO,
} from "@/types/questionnaire";
import { QuestionTypeRegistry } from "@/config/question-type-registry";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useState, useEffect, useRef } from "react";
import { useDrag, useDrop } from "react-dnd";
import { GripVertical, Trash2, HelpCircle, Pencil, Check } from "lucide-react";
import { genUUID } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectItem,
  SelectTrigger,
  SelectContent,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Textarea } from "@/components/ui/textarea";
import { useQuestionEditStore } from "@/store/question-edit";

const OPTION_ITEM_TYPE = "option-item";
const LOGIC_EXPR_ITEM_TYPE = "logic-expr-item";

interface QuestionPropsEditorProps {
  selectedQuestion: QuestionResponseDTO;
  onEdit: (field: keyof QuestionProps, value: any) => void;
}

interface QuestionnairePropsEditorProps {
  questionnaire: QuestionnaireResponseDTO;
  onEdit: (field: keyof QuestionnaireResponseDTO, value: any) => void;
  logicExpressions: QuestionLogicExpression[];
  onLogicExpressionsChange: (exprs: QuestionLogicExpression[]) => void;
  questions: QuestionResponseDTO[];
  questionnaireId: string;
}

function DraggableOptionItem({
  id,
  index,
  moveOption,
  children,
}: {
  id: string;
  index: number;
  moveOption: (from: number, to: number) => void;
  children: React.ReactNode;
}) {
  const ref = React.useRef<HTMLDivElement>(null);
  const [, drop] = useDrop({
    accept: OPTION_ITEM_TYPE,
    hover(item: { index: number }, monitor) {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;
      moveOption(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });
  const [{ isDragging }, drag, preview] = useDrag({
    type: OPTION_ITEM_TYPE,
    item: { id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drag(drop(ref));
  return (
    <div
      ref={ref}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className="flex items-center gap-2"
    >
      {children}
    </div>
  );
}

function OptionsEditor({
  value = [],
  onChange,
  questionType,
}: {
  value: OptionItem[];
  onChange: (val: OptionItem[]) => void;
  questionType: string;
}) {
  const showHasInput =
    questionType === QuestionType.RadioInput ||
    questionType === QuestionType.CheckboxInput;
  const withUuid = value.map((opt) =>
    opt.value ? opt : { ...opt, value: genUUID() }
  );
  const [options, setOptions] = useState<OptionItem[]>(withUuid);

  useEffect(() => {
    setOptions(
      value.map((opt) => (opt.value ? opt : { ...opt, value: genUUID() }))
    );
  }, [value]);

  useEffect(() => {
    if (value.some((opt) => !opt.value)) {
      onChange(withUuid);
    }
    // eslint-disable-next-line
  }, []);

  console.log("showHasInput", questionType, showHasInput);
  // 拖拽排序
  const moveOption = (from: number, to: number) => {
    if (from === to) return;
    const updated = [...options];
    const [removed] = updated.splice(from, 1);
    updated.splice(to, 0, removed);
    setOptions(updated);
    onChange(updated);
  };

  const handleAdd = () => {
    const newOption: OptionItem = {
      label: "",
      value: genUUID(),
      hasInput: false,
    };
    const newOptions = [...options, newOption];
    setOptions(newOptions);
    onChange(newOptions);
  };

  const handleLabelChange = (idx: number, label: string) => {
    const newOptions = options.map((opt, i) =>
      i === idx ? { ...opt, label } : opt
    );
    setOptions(newOptions);
    onChange(newOptions);
  };

  const handleHasInputChange = (idx: number, checked: boolean) => {
    const newOptions = options.map((opt, i) =>
      i === idx ? { ...opt, hasInput: checked } : opt
    );
    setOptions(newOptions);
    onChange(newOptions);
  };

  const handleDelete = (idx: number) => {
    const newOptions = options.filter((_, i) => i !== idx);
    setOptions(newOptions);
    onChange(newOptions);
  };

  return (
    <div className="space-y-3">
      {/* 表头说明 */}
      <div className="mb-2 flex items-center gap-2 text-sm font-semibold text-gray-800 px-1">
        <span className="flex-1">选项内容</span>
        {showHasInput && <span className="w-20 text-center">允许补充</span>}
        <span className="w-10 text-center">操作</span>
      </div>
      {options.map((opt, idx) => (
        <DraggableOptionItem
          key={opt.value}
          id={opt.value}
          index={idx}
          moveOption={moveOption}
        >
          <div className="flex items-center gap-2 px-1">
            <span className="cursor-grab text-gray-400 hover:text-gray-600">
              <GripVertical size={16} />
            </span>
            <input
              className={`input input-sm min-w-0 border rounded px-2 py-1 ${
                showHasInput ? "w-22" : "w-43"
              }`}
              value={opt.label}
              onChange={(e) => handleLabelChange(idx, e.target.value)}
              placeholder={`选项${idx + 1}`}
            />
            {showHasInput && (
              <div className="w-20 flex justify-center">
                <Switch
                  checked={!!opt.hasInput}
                  onCheckedChange={(checked) =>
                    handleHasInputChange(idx, checked)
                  }
                  id={`hasInput-${opt.value}`}
                />
              </div>
            )}
            <div className="w-10 flex justify-center">
              <button
                type="button"
                onClick={() => handleDelete(idx)}
                className="text-red-500 hover:bg-red-50 rounded p-1"
                title="删除"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        </DraggableOptionItem>
      ))}
      <button
        type="button"
        onClick={handleAdd}
        className="text-xs text-blue-500 px-2 py-1"
      >
        新增选项
      </button>
    </div>
  );
}

const renderFormControl = (
  prop: any,
  value: any,
  onChange: (val: any) => void,
  selectedQuestion?: QuestionResponseDTO
) => {
  if (prop.type === "options") {
    return (
      <OptionsEditor
        value={value}
        onChange={onChange}
        questionType={selectedQuestion?.type || ""}
      />
    );
  }
  if (prop.type === "color") {
    return (
      <input
        type="color"
        value={value || "#333333"}
        onChange={(e) => onChange(e.target.value)}
        style={{
          width: 40,
          height: 24,
          padding: 0,
          border: "none",
          background: "none",
        }}
      />
    );
  }
  if (prop.type === "input" && prop.name === "code") {
    // 判断是否可编辑
    const isCodeEditable =
      selectedQuestion &&
      (selectedQuestion.crud === 1 ||
        (typeof selectedQuestion.id === "string" &&
          selectedQuestion.id.startsWith("local_")));
    return (
      <Input
        value={value || ""}
        onChange={(e) => {
          const val = e.target.value.replace(/[^a-zA-Z0-9_]/g, "");
          onChange(val);
        }}
        placeholder="请输入英文编码"
        disabled={!isCodeEditable}
      />
    );
  }
  switch (prop.type) {
    case "Switch":
      return <Switch checked={!!value} onCheckedChange={onChange} />;
    case "number":
      return (
        <Input
          type="number"
          value={value || ""}
          onChange={(e) => onChange(e.target.valueAsNumber)}
        />
      );
    case "input":
    default:
      return (
        <Input
          value={value || ""}
          disabled={prop.disabled}
          onChange={(e) => onChange(e.target.value)}
        />
      );
  }
};

export function QuestionPropsEditor({
  selectedQuestion,
  onEdit,
}: QuestionPropsEditorProps) {
  // 判空处理
  if (!selectedQuestion) {
    return (
      <div className="text-muted-foreground text-center py-10 select-none">
        请在中间选择题目
      </div>
    );
  }

  const registryItem =
    QuestionTypeRegistry[
      selectedQuestion.type as keyof typeof QuestionTypeRegistry
    ];

  if (!registryItem) {
    return <div className="text-muted-foreground">未知题型</div>;
  }

  return (
    <TooltipProvider>
      <div className="space-y-4">
        {registryItem.props?.map((prop: any) => (
          <div key={prop.name}>
            <Label className="block text-sm font-semibold mb-1">
              {prop.label}
            </Label>
            {renderFormControl(
              prop,
              selectedQuestion.props[
                prop.name as keyof typeof selectedQuestion.props
              ],
              (value) => {
                onEdit(prop.name as keyof QuestionProps, value);
              },
              selectedQuestion // 传递selectedQuestion用于判断code可编辑性
            )}
          </div>
        ))}
      </div>
    </TooltipProvider>
  );
}

export function QuestionnairePropsEditor({
  questionnaire,
  onEdit,
  logicExpressions,
  onLogicExpressionsChange,
  questions,
}: QuestionnairePropsEditorProps) {
  const [logicModalOpen, setLogicModalOpen] = useState(false);
  return (
    <div className="space-y-4">
      <div>
        <Label className="block text-sm font-semibold mb-1">问卷标题</Label>
        <Input
          value={questionnaire.title}
          onChange={(e) => onEdit("title", e.target.value)}
          placeholder="请输入问卷标题"
        />
      </div>
      <div>
        <Label className="block text-sm font-semibold mb-1">描述</Label>
        <Input
          value={questionnaire.description || ""}
          onChange={(e) => onEdit("description", e.target.value)}
          placeholder="请输入描述"
        />
      </div>
      <div>
        <Label className="block text-sm font-semibold mb-1">开始时间</Label>
        <Input
          type="datetime-local"
          value={questionnaire.startTime || ""}
          onChange={(e) => onEdit("startTime", e.target.value)}
        />
      </div>
      <div>
        <Label className="block text-sm font-semibold mb-1">结束时间</Label>
        <Input
          type="datetime-local"
          value={questionnaire.endTime || ""}
          onChange={(e) => onEdit("endTime", e.target.value)}
        />
      </div>
      <Button onClick={() => setLogicModalOpen(true)}>编辑问题表达式</Button>
      {logicModalOpen && (
        <LogicExpressionModal
          open={logicModalOpen}
          onOpenChange={setLogicModalOpen}
          logicExpressions={logicExpressions}
          questions={questions}
          onChange={onLogicExpressionsChange}
          questionnaireId={questionnaire.id}
        />
      )}
    </div>
  );
}

export function PropertyPanel({
  questionnaire,
  onQuestionnaireEdit,
  selectedQuestion,
  onQuestionEdit,
  questions,
}: {
  questionnaire: QuestionnaireResponseDTO;
  questions: QuestionResponseDTO[];
  onQuestionnaireEdit: (
    field: keyof QuestionnaireResponseDTO,
    value: any
  ) => void;
  selectedQuestion: QuestionResponseDTO;
  onQuestionEdit: (field: keyof QuestionProps, value: any) => void;
}) {
  const { updateLogicExpressions } = useQuestionEditStore();
  return (
    <Tabs defaultValue="questionnaire" className="w-full flex-1 flex flex-col">
      <TabsList className="w-full">
        <TabsTrigger value="questionnaire" className="flex-1">
          问卷属性
        </TabsTrigger>
        <TabsTrigger value="question" className="flex-1">
          题目属性
        </TabsTrigger>
      </TabsList>
      <TabsContent value="questionnaire" className="w-full h-full">
        <QuestionnairePropsEditor
          questionnaire={questionnaire}
          onEdit={onQuestionnaireEdit}
          logicExpressions={questionnaire.logicExpressions ?? []}
          onLogicExpressionsChange={updateLogicExpressions}
          questions={questions}
          questionnaireId={questionnaire.id}
        />
      </TabsContent>
      <TabsContent value="question" className="w-full h-full">
        <QuestionPropsEditor
          selectedQuestion={selectedQuestion}
          onEdit={onQuestionEdit}
        />
      </TabsContent>
    </Tabs>
  );
}

function validateExpression(expr: string, codes: string[]): string | null {
  // console.log("exprexprexpr", expr, codes);
  // 1. 必须包含至少一个题目code
  const codeMatches = expr.match(/question_\w+/g) || [];
  if (codeMatches.length === 0) {
    return "表达式必须包含至少一个题目code（如 question_1001）";
  }
  // 2. 必须包含至少一个运算符
  if (!/[=><!&|]+/.test(expr)) {
    return "表达式必须包含至少一个运算符（如 ==, >, <, &&, || 等)";
  }
  // 3. 检查非法字符
  if (!/^[\w\s\d_><=!&|()]+$/.test(expr)) {
    return "表达式包含非法字符";
  }
  // 4. 尝试用 Function 解析
  try {
    // eslint-disable-next-line no-new-func
    new Function("return " + expr);
  } catch {
    return "表达式语法错误";
  }
  return null; // 合法
}

function DraggableLogicExprItem({
  id,
  index,
  moveExpr,
  children,
}: {
  id: string;
  index: number;
  moveExpr: (from: number, to: number) => void;
  children: React.ReactNode;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const [, drop] = useDrop({
    accept: LOGIC_EXPR_ITEM_TYPE,
    hover(item: { index: number }, monitor) {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;
      moveExpr(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });
  const [{ isDragging }, drag] = useDrag({
    type: LOGIC_EXPR_ITEM_TYPE,
    item: { id, index },
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
  });
  drag(drop(ref));
  return (
    <div
      ref={ref}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className="flex items-center gap-2"
    >
      {children}
    </div>
  );
}

function LogicExpressionModal({
  open,
  onOpenChange,
  logicExpressions,
  questions,
  onChange,
  questionnaireId,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  logicExpressions: QuestionLogicExpression[];
  questions: QuestionResponseDTO[];
  onChange: (exprs: QuestionLogicExpression[]) => void;
  questionnaireId: string;
}) {
  const [localExpressions, setLocalExpressions] =
    useState<QuestionLogicExpression[]>(logicExpressions);
  const [exprErrors, setExprErrors] = useState<(string | null)[]>([]);
  const exprRefs = useRef<(HTMLTextAreaElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [editingIdx, setEditingIdx] = useState<number | null>(null);

  useEffect(() => {
    setLocalExpressions(logicExpressions);
    setExprErrors(logicExpressions.map(() => null));
  }, [logicExpressions]);

  const codes = questions
    .map((q) => q.props.code)
    .filter((c): c is string => !!c);

  const handleAdd = () => {
    setLocalExpressions((prev) => {
      const next = [
        ...prev,
        {
          questionnaireId,
          targetQuestionId: "",
          expression: "",
          actionType: "SHOW" as "SHOW",
          priority: 0,
          crud: 1 as 1,
        },
      ];
      setEditingIdx(next.length - 1);
      return next;
    });
    setExprErrors((errors) => [...errors, null]);
  };

  const handleEdit = (
    idx: number,
    field: keyof QuestionLogicExpression,
    value: any
  ) => {
    setLocalExpressions((exprs) =>
      exprs.map((item, i) =>
        i === idx ? { ...item, [field]: value, crud: item.id ? 2 : 1 } : item
      )
    );
    if (field === "expression") {
      setExprErrors((errors) =>
        errors.map((err, i) => (i === idx ? null : err))
      );
    }
  };

  const handleDelete = (idx: number) => {
    setLocalExpressions(
      (exprs) =>
        exprs
          .map((item, i) =>
            i === idx ? (item.id ? { ...item, crud: 3 } : null) : item
          )
          .filter(Boolean) as QuestionLogicExpression[]
    );
    setExprErrors((errors) => errors.filter((_, i) => i !== idx));
  };

  // 插入内容到光标处
  const insertAtCursor = (idx: number, text: string) => {
    const ref = exprRefs.current[idx];
    if (!ref) return;
    const start = ref.selectionStart || 0;
    const end = ref.selectionEnd || 0;
    const oldVal = localExpressions[idx].expression || "";
    const newVal = oldVal.slice(0, start) + text + oldVal.slice(end);
    handleEdit(idx, "expression", newVal);
    setTimeout(() => {
      ref.focus();
      ref.setSelectionRange(start + text.length, start + text.length);
    }, 0);
  };

  // 校验表达式
  const handleValidate = (idx: number) => {
    const expr = localExpressions[idx].expression;
    const err = validateExpression(expr, codes);
    setExprErrors((errors) => errors.map((e, i) => (i === idx ? err : e)));
    if (!err) {
      toast.success("表达式合法");
    } else {
      toast.error(err);
    }
  };

  const operatorButtons = [
    { label: "==", value: " == " },
    { label: ">", value: " > " },
    { label: "<", value: " < " },
    { label: ">=", value: " >= " },
    { label: "<=", value: " <= " },
    { label: "&&", value: " && " },
    { label: "||", value: " || " },
  ];

  // 拖拽排序
  const moveExpr = (from: number, to: number) => {
    if (from === to) return;
    setLocalExpressions((exprs) => {
      const updated = [...exprs];
      const [removed] = updated.splice(from, 1);
      // 如果被移动的表达式有 id（已保存过），则设置为修改状态
      if (removed.id) {
        removed.crud = 2;
      }
      updated.splice(to, 0, removed);
      
      // 检查所有受影响的表达式（从 min(from, to) 到 max(from, to)），如果有 id 都设置为修改状态
      const start = Math.min(from, to);
      const end = Math.max(from, to);
      for (let i = start; i <= end; i++) {
        if (updated[i] && updated[i].id) {
          updated[i].crud = 2;
        }
      }
      
      return updated;
    });
    setExprErrors((errors) => {
      const updated = [...errors];
      const [removed] = updated.splice(from, 1);
      updated.splice(to, 0, removed);
      return updated;
    });
  };

  const handleSave = () => {
    let hasError = false;
    const newExprErrors = localExpressions.map((item, idx) => {
      if (item.crud === 3) return null;
      if (!item.targetQuestionId) {
        hasError = true;
        return "请选择目标题目";
      }
      if (!item.expression || !item.expression.trim()) {
        hasError = true;
        return "请输入表达式";
      }
      const exprErr = validateExpression(item.expression, codes);
      if (exprErr) {
        hasError = true;
        return exprErr;
      }
      if (!item.actionType) {
        hasError = true;
        return "请选择动作类型";
      }
      return null;
    });
    setExprErrors(newExprErrors);
    if (hasError) {
      toast.error("请完善所有必填项和合法表达式后再保存");
      return;
    }
    const withPriority = localExpressions.map((item, idx) =>
      item.crud === 3 ? item : { ...item, priority: idx }
    );
    onChange(withPriority);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="!max-w-6xl w-full"
        style={{ maxWidth: "72rem" }}
      >
        <DialogTitle>编辑问题逻辑表达式</DialogTitle>
        <DialogDescription>
          在此编辑所有逻辑表达式，支持拖拽排序、表达式校验等。
        </DialogDescription>
        <div className="space-y-2 max-h-[60vh] overflow-y-auto">
          {localExpressions.map(
            (item, idx) =>
              item.crud !== 3 && (
                <DraggableLogicExprItem
                  key={item.id ?? idx}
                  id={String(item.id ?? idx)}
                  index={idx}
                  moveExpr={moveExpr}
                >
                  <span className="cursor-grab text-gray-400 hover:text-gray-600">
                    <GripVertical size={16} />
                  </span>
                  <Select
                    value={item.targetQuestionId}
                    onValueChange={(code) =>
                      handleEdit(idx, "targetQuestionId", code)
                    }
                    disabled={editingIdx !== idx}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="请选择题目" />
                    </SelectTrigger>
                    <SelectContent>
                      {questions
                        .map((q) => (
                          <SelectItem
                            key={q.id}
                            value={q.id!!}
                          >
                            {q.props.title}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <div className="flex flex-col gap-1 flex-1 relative">
                    <Textarea
                      ref={(el) => {
                        exprRefs.current[idx] =
                          el as HTMLTextAreaElement | null;
                      }}
                      value={item.expression}
                      onChange={(e) =>
                        handleEdit(idx, "expression", e.target.value)
                      }
                      placeholder="如：question_1001 == 1 && question_1002 > 3"
                      className={exprErrors[idx] ? "border-red-500" : ""}
                      disabled={editingIdx !== idx}
                    />
                    {editingIdx === idx && (
                      <div className="flex gap-1 mt-1">
                        <Select
                          onValueChange={(code) =>
                            insertAtCursor(idx, code || "")
                          }
                        >
                          <SelectTrigger>
                            <span>插入题目</span>
                          </SelectTrigger>
                          <SelectContent>
                            {questions
                              .filter((q) => !!q.props.code)
                              .map((q) => (
                                <SelectItem
                                  key={q.id}
                                  value={"question_" + q.props.code}
                                >
                                  {q.props.title}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                        {operatorButtons.map((op) => (
                          <Button
                            key={op.label}
                            variant="outline"
                            size="sm"
                            onClick={() => insertAtCursor(idx, op.value)}
                          >
                            {op.label}
                          </Button>
                        ))}
                        <Button
                          variant="secondary"
                          onClick={() => handleValidate(idx)}
                        >
                          验证表达式
                        </Button>
                      </div>
                    )}
                    {exprErrors[idx] === null ? null : (
                      <div
                        className={
                          exprErrors[idx]
                            ? "text-red-500 text-xs"
                            : "text-green-600 text-xs"
                        }
                      >
                        {exprErrors[idx] || "表达式合法"}
                      </div>
                    )}
                  </div>
                  <Select
                    value={item.actionType}
                    onValueChange={(v) => handleEdit(idx, "actionType", v)}
                    disabled={editingIdx !== idx}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择动作" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SHOW">显示</SelectItem>
                      <SelectItem value="HIDE">隐藏</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() =>
                      editingIdx === idx
                        ? setEditingIdx(null)
                        : setEditingIdx(idx)
                    }
                    className="ml-1"
                  >
                    {editingIdx === idx ? (
                      <Check size={16} />
                    ) : (
                      <Pencil size={16} />
                    )}
                  </Button>
                  <Button
                    size="icon"
                    variant="destructive"
                    onClick={() => handleDelete(idx)}
                    className="ml-1"
                  >
                    <Trash2 size={14} />
                  </Button>
                </DraggableLogicExprItem>
              )
          )}
          <Button onClick={handleAdd} variant="outline">
            新增表达式
          </Button>
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
