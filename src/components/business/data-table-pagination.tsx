"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
} from "@/components/ui/pagination";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Table } from "@tanstack/react-table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
}

export function DataTablePagination<TData>({
  table,
}: DataTablePaginationProps<TData>) {
  const totalPages = table.getPageCount();
  const currentPage = table.getState().pagination.pageIndex;

  if (totalPages === 0) {
    return null;
  }

  // 计算显示的页码范围
  const getVisiblePages = () => {
    const maxVisible = 5; // 最多显示5个页码
    const half = Math.floor(maxVisible / 2);

    let start = Math.max(0, currentPage - half);
    let end = Math.min(totalPages - 1, start + maxVisible - 1);

    // 如果末尾不够，调整开始位置
    if (end - start + 1 < maxVisible) {
      start = Math.max(0, end - maxVisible + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
      <div className="flex items-center space-x-2">
        <span className="text-sm text-muted-foreground whitespace-nowrap">
          第 {currentPage + 1} 页，共 {totalPages} 页
        </span>
      </div>

      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <Button
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              variant="outline"
              size="icon"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </PaginationItem>

          {/* 第一页 */}
          {visiblePages[0] > 0 && (
            <>
              <PaginationItem>
                <PaginationLink
                  onClick={() => table.setPageIndex(0)}
                  isActive={currentPage === 0}
                >
                  1
                </PaginationLink>
              </PaginationItem>
              {visiblePages[0] > 1 && (
                <PaginationItem>
                  <span className="px-2">...</span>
                </PaginationItem>
              )}
            </>
          )}

          {/* 可见页码 */}
          {visiblePages.map((pageIndex) => (
            <PaginationItem key={pageIndex}>
              <PaginationLink
                onClick={() => table.setPageIndex(pageIndex)}
                isActive={currentPage === pageIndex}
              >
                {pageIndex + 1}
              </PaginationLink>
            </PaginationItem>
          ))}

          {/* 最后一页 */}
          {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
            <>
              {visiblePages[visiblePages.length - 1] < totalPages - 2 && (
                <PaginationItem>
                  <span className="px-2">...</span>
                </PaginationItem>
              )}
              <PaginationItem>
                <PaginationLink
                  onClick={() => table.setPageIndex(totalPages - 1)}
                  isActive={currentPage === totalPages - 1}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            </>
          )}

          <PaginationItem>
            <Button
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              variant="outline"
              size="icon"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </PaginationItem>
        </PaginationContent>
      </Pagination>

      <div className="ml-4">
        <Select
          value={`${table.getState().pagination.pageSize}`}
          onValueChange={(value) => {
            table.setPageSize(Number(value));
          }}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="每页条数" />
          </SelectTrigger>
          <SelectContent>
            {[10, 20, 50, 100].map((pageSize) => (
              <SelectItem key={pageSize} value={`${pageSize}`}>
                每页 {pageSize} 条
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
