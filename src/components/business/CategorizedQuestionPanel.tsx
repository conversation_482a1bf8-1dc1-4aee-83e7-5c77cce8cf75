import { useDrag } from 'react-dnd';
import { QuestionType, QuestionModule } from '@/types/questionnaire';
import { QuestionTypeRegistry } from '@/config/question-type-registry';
import { ItemTypes } from './dnd-types';
import { 
  TipIcon, InputIcon, ChoiceIcon, DateIcon, AreaIcon, SpecialIcon,
  TextIcon, SingleLineIcon, MultiLineIcon, NumberIcon, RadioIcon, 
  CheckboxIcon, SelectIcon, SliderIcon, DatePickerIcon, TimeIcon, 
  LocationIcon, StarIcon, SexIcon 
} from './QuestionTypeIcons';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

interface CategorizedQuestionPanelProps {
  onQuestionAdd: (type: QuestionType, index: number) => void;
}

// 题型分类配置
const questionCategories = [
  {
    key: QuestionModule.Tip,
    label: "提示类",
    icon: TipIcon,
    color: "text-blue-600 dark:text-blue-400",
    bgColor: "bg-blue-50 dark:bg-blue-950/30",
    borderColor: "border-blue-200 dark:border-blue-800",
    types: [
      { type: QuestionType.Text, label: "文本", icon: TextIcon }
    ]
  },
  {
    key: QuestionModule.Input,
    label: "输入类",
    icon: InputIcon,
    color: "text-green-600 dark:text-green-400",
    bgColor: "bg-green-50 dark:bg-green-950/30",
    borderColor: "border-green-200 dark:border-green-800",
    types: [
      { type: QuestionType.Input, label: "单行文本", icon: SingleLineIcon },
      { type: QuestionType.Textarea, label: "多行文本", icon: MultiLineIcon },
      { type: QuestionType.InputNumber, label: "数字输入", icon: NumberIcon },
    ]
  },
  {
    key: QuestionModule.Choice,
    label: "选择题",
    icon: ChoiceIcon,
    color: "text-purple-600 dark:text-purple-400",
    bgColor: "bg-purple-50 dark:bg-purple-950/30",
    borderColor: "border-purple-200 dark:border-purple-800",
    types: [
      { type: QuestionType.Radio, label: "单选题", icon: RadioIcon },
      { type: QuestionType.RadioInput, label: "单选输入题", icon: RadioIcon },
      { type: QuestionType.Checkbox, label: "多选题", icon: CheckboxIcon },
      { type: QuestionType.CheckboxInput, label: "多选输入题", icon: RadioIcon },
      { type: QuestionType.Select, label: "下拉选择", icon: SelectIcon },
      { type: QuestionType.Slider, label: "拖拽选择", icon: SliderIcon }
    ]
  },
  {
    key: QuestionModule.Date,
    label: "日期类",
    icon: DateIcon,
    color: "text-orange-600 dark:text-orange-400",
    bgColor: "bg-orange-50 dark:bg-orange-950/30",
    borderColor: "border-orange-200 dark:border-orange-800",
    types: [
      { type: QuestionType.Date, label: "日期选择", icon: DatePickerIcon },
      { type: QuestionType.Time, label: "时间选择", icon: TimeIcon },
      { type: QuestionType.Year, label: "年份选择", icon: DatePickerIcon },
      { type: QuestionType.Month, label: "月份选择", icon: DatePickerIcon },
      { type: QuestionType.DateTime, label: "日期时间", icon: TimeIcon },
    ]
  },
  {
    key: QuestionModule.Area,
    label: "区域类",
    icon: AreaIcon,
    color: "text-red-600 dark:text-red-400",
    bgColor: "bg-red-50 dark:bg-red-950/30",
    borderColor: "border-red-200 dark:border-red-800",
    types: [
      { type: QuestionType.Province, label: "省份选择", icon: LocationIcon },
      { type: QuestionType.ProvinceCity, label: "省市选择", icon: LocationIcon },
      { type: QuestionType.Area, label: "省市区选择", icon: LocationIcon }
    ]
  },
  {
    key: QuestionModule.Special,
    label: "特殊类型",
    icon: SpecialIcon,
    color: "text-indigo-600 dark:text-indigo-400",
    bgColor: "bg-indigo-50 dark:bg-indigo-950/30",
    borderColor: "border-indigo-200 dark:border-indigo-800",
    types: [
      { type: QuestionType.SEX, label: "性别", icon: SexIcon }
    ]
  }
];

function DraggableQuestionType({ type, label, icon: Icon }: { 
  type: QuestionType; 
  label: string; 
  icon: React.ComponentType 
}) {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.NEW_QUESTION,
    item: { type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return (
    <div
      ref={drag as any}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className="flex items-center gap-2 px-3 py-2.5 rounded-lg cursor-grab hover:bg-accent active:bg-accent/80 transition select-none text-sm border border-transparent hover:border-border"
    >
      <Icon />
      <span className="font-medium text-foreground truncate">{label}</span>
    </div>
  );
}

export function CategorizedQuestionPanel({ onQuestionAdd }: CategorizedQuestionPanelProps) {
  const [openCategories, setOpenCategories] = useState<Set<QuestionModule>>(
    new Set([QuestionModule.Input, QuestionModule.Choice]) // 默认展开输入类和选择题
  );

  const toggleCategory = (category: QuestionModule) => {
    const newOpenCategories = new Set(openCategories);
    if (newOpenCategories.has(category)) {
      newOpenCategories.delete(category);
    } else {
      newOpenCategories.add(category);
    }
    setOpenCategories(newOpenCategories);
  };

  return (
    <aside className="w-72 h-full rounded-2xl bg-card/70 shadow-md p-4 flex flex-col gap-3 backdrop-blur border border-border">
      <h2 className="text-base font-semibold mb-2 text-card-foreground">题型面板</h2>
      
      <div className="flex flex-col gap-2 flex-1 overflow-y-auto">
        {questionCategories.map((category) => {
          const CategoryIcon = category.icon;
          const isOpen = openCategories.has(category.key);
          
          return (
            <Collapsible
              key={category.key}
              open={isOpen}
              onOpenChange={() => toggleCategory(category.key)}
            >
              <CollapsibleTrigger className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-accent transition">
                <div className="flex items-center gap-2">
                  <CategoryIcon />
                  <span className={`font-medium ${category.color}`}>{category.label}</span>
                </div>
                <ChevronDown 
                  className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
                />
              </CollapsibleTrigger>
              
              <CollapsibleContent className="mt-2">
                <div className={`p-2 rounded-lg ${category.bgColor} border ${category.borderColor}`}>
                  <div className="grid grid-cols-2 gap-2">
                    {category.types.map((questionType) => (
                      <DraggableQuestionType
                        key={questionType.type}
                        type={questionType.type}
                        label={questionType.label}
                        icon={questionType.icon}
                      />
                    ))}
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </div>
    </aside>
  );
} 