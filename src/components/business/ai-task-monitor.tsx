import React, { useEffect } from 'react';
import { useAITaskStore, TaskStatus } from '@/store/ai-task';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

/**
 * AI 任务监控组件
 * 用于监控全局 AI 任务状态，在任务完成时显示通知
 * 应该在应用的根组件中使用
 */
export const AITaskMonitor: React.FC = () => {
  const { activeTasks, removeTask } = useAITaskStore();
  const queryClient = useQueryClient();

  useEffect(() => {
    // 监听任务状态变化
    const checkTasks = () => {
      activeTasks.forEach((task, taskId) => {
        if (task.status === TaskStatus.COMPLETED) {
          // 任务完成通知
          toast.success(
            '🎉 AI 问卷生成完成！',
            {
              description: '您可以在问卷列表中查看新生成的问卷',
              duration: 5000,
              action: {
                label: '查看问卷',
                onClick: () => {
                  // 刷新问卷列表
                  queryClient.invalidateQueries({ queryKey: ["questionnaires"] });
                  // 可以添加导航到问卷列表的逻辑
                  window.location.href = '/questionnaire';
                }
              }
            }
          );
          
          // 延迟清理任务
          setTimeout(() => {
            removeTask(taskId);
          }, 10000); // 10秒后清理
          
        } else if (task.status === TaskStatus.FAILED) {
          // 任务失败通知
          toast.error(
            '❌ AI 问卷生成失败',
            {
              description: task.error || '生成过程中出现错误，请重试',
              duration: 5000,
              action: {
                label: '重试',
                onClick: () => {
                  // 可以添加重试逻辑
                  removeTask(taskId);
                }
              }
            }
          );
          
          // 延迟清理任务
          setTimeout(() => {
            removeTask(taskId);
          }, 5000);
        }
      });
    };

    // 立即检查一次
    checkTasks();

    // 设置定期检查
    const interval = setInterval(checkTasks, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [activeTasks, removeTask, queryClient]);

  // 这个组件不渲染任何 UI
  return null;
};

/**
 * 任务状态指示器组件
 * 显示当前正在进行的任务数量
 */
export const AITaskIndicator: React.FC = () => {
  const { activeTasks } = useAITaskStore();
  
  const runningTasks = Array.from(activeTasks.values()).filter(
    task => task.status === TaskStatus.PENDING || task.status === TaskStatus.PROCESSING
  );

  if (runningTasks.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
        <span className="text-sm">
          {runningTasks.length} 个 AI 任务进行中
        </span>
      </div>
    </div>
  );
};
