"use client";

import React, { useEffect, useRef } from 'react';
import { useAITaskStore, TaskStatus } from '@/store/ai-task';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

/**
 * AI 任务监控组件
 * 用于监控全局 AI 任务状态，在任务完成时显示通知
 * 应该在应用的根组件中使用
 */
export const AITaskMonitor: React.FC = () => {
  const { activeTasks, removeTask } = useAITaskStore();
  const queryClient = useQueryClient();

  // 用于跟踪已经通知过的任务，避免重复通知
  const notifiedTasksRef = useRef<Set<string>>(new Set());
  const previousTasksRef = useRef<Map<string, TaskStatus>>(new Map());

  useEffect(() => {
    // 检查任务状态变化
    activeTasks.forEach((task, taskId) => {
      const previousStatus = previousTasksRef.current.get(taskId);
      const hasNotified = notifiedTasksRef.current.has(taskId);

      // 只有当状态发生变化且未通知过时才处理
      if (previousStatus !== task.status && !hasNotified) {
        if (task.status === TaskStatus.SUCCESS) {
          // 标记为已通知
          notifiedTasksRef.current.add(taskId);

          // 自动刷新问卷列表
          queryClient.invalidateQueries({ queryKey: ["questionnaires"] });

          // 任务完成通知
          toast.success(
            '🎉 AI 问卷创建完成！',
            {
              description: '问卷已成功创建，列表已自动刷新',
              duration: 5000,
              action: {
                label: '查看问卷',
                onClick: () => {
                  // 可以添加导航到问卷列表的逻辑
                  window.location.href = '/questionnaire';
                }
              }
            }
          );

          // 延迟清理任务
          setTimeout(() => {
            removeTask(taskId);
            notifiedTasksRef.current.delete(taskId);
            previousTasksRef.current.delete(taskId);
          }, 10000); // 10秒后清理

        } else if (task.status === TaskStatus.FAILED) {
          // 标记为已通知
          notifiedTasksRef.current.add(taskId);

          // 任务失败通知
          toast.error(
            '❌ AI 问卷创建失败',
            {
              description: task.error || '创建过程中出现错误，请重新尝试',
              duration: 5000,
              action: {
                label: '知道了',
                onClick: () => {
                  removeTask(taskId);
                  notifiedTasksRef.current.delete(taskId);
                  previousTasksRef.current.delete(taskId);
                }
              }
            }
          );

          // 延迟清理任务
          setTimeout(() => {
            removeTask(taskId);
            notifiedTasksRef.current.delete(taskId);
            previousTasksRef.current.delete(taskId);
          }, 5000);
        }
      }

      // 更新之前的状态记录
      previousTasksRef.current.set(taskId, task.status);
    });

    // 清理已删除任务的记录
    const currentTaskIds = new Set(activeTasks.keys());
    previousTasksRef.current.forEach((_, taskId) => {
      if (!currentTaskIds.has(taskId)) {
        previousTasksRef.current.delete(taskId);
        notifiedTasksRef.current.delete(taskId);
      }
    });
  }, [activeTasks, removeTask, queryClient]);

  // 这个组件不渲染任何 UI
  return null;
};

/**
 * 任务状态指示器组件
 * 显示当前正在进行的任务数量
 */
export const AITaskIndicator: React.FC = () => {
  const { activeTasks } = useAITaskStore();
  
  const runningTasks = Array.from(activeTasks.values()).filter(
    task => task.status === TaskStatus.PENDING || task.status === TaskStatus.PROCESSING
  );

  if (runningTasks.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
        <span className="text-sm">
          {runningTasks.length} 个 AI 任务进行中
        </span>
      </div>
    </div>
  );
};
