import { useDrag, useDrop } from 'react-dnd';
import { But<PERSON> } from "@/components/ui/button";
import { GripVertical } from "lucide-react";
import { QuestionResponseDTO, QuestionType } from "@/types/questionnaire";
import { QuestionTypeRegistry } from '@/config/question-type-registry';
import { useRef, useState } from 'react';
import { ItemTypes, DragItem } from './dnd-types';

interface DraggableQuestionProps {
  question: QuestionResponseDTO;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onMove: (dragIndex: number, hoverIndex: number) => void;
  onQuestionAdd?: (type: QuestionType, index: number) => void;
}

export function DraggableQuestion({ 
  question, 
  index, 
  isSelected,
  onSelect,
  onDelete,
  onMove,
  onQuestionAdd
}: DraggableQuestionProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [insertPosition, setInsertPosition] = useState<'top' | 'bottom' | null>(null);

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.QUESTION,
    item: { type: ItemTypes.QUESTION, index, id: question.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.QUESTION, ItemTypes.NEW_QUESTION],
    hover: (item: DragItem, monitor) => {
      if (!ref.current) return;

      // 处理题目排序
      if (monitor.getItemType() === ItemTypes.QUESTION) {
        const dragIndex = item.index;
        const hoverIndex = index;

        // Don't replace items with themselves
        if (dragIndex === hoverIndex) return;

        // Time to actually perform the action
        onMove(dragIndex, hoverIndex);

        // Note: we're mutating the monitor item here!
        // Generally it's better to avoid mutations,
        // but it's good here for the sake of performance
        // to avoid expensive index searches.
        item.index = hoverIndex;
      }

      // 处理新题目插入位置指示
      if (monitor.getItemType() === ItemTypes.NEW_QUESTION) {
        const hoverBoundingRect = ref.current?.getBoundingClientRect();
        if (!hoverBoundingRect) return;

        // Get vertical middle
        const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

        // Get mouse position
        const clientOffset = monitor.getClientOffset();
        if (!clientOffset) return;

        // Get pixels to the top
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;

        // Only perform the move when the mouse has crossed half of the items height
        // When dragging downwards, only move when the cursor is below 50%
        // When dragging upwards, only move when the cursor is above 50%

        // Dragging downwards
        if (hoverClientY < hoverMiddleY) {
          setInsertPosition('top');
        } else {
          setInsertPosition('bottom');
        }
      }
    },
    drop: (item: DragItem, monitor) => {
      if (!monitor.isOver({ shallow: true })) return;
      
      // 处理新题目插入
      if (monitor.getItemType() === ItemTypes.NEW_QUESTION && onQuestionAdd) {
        const insertIndex = insertPosition === 'top' ? index : index + 1;
        onQuestionAdd(item.type as QuestionType, insertIndex);
      }
      setInsertPosition(null);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    })
  });

  // 当不再悬停时重置插入位置
  if (!isOver && insertPosition !== null) {
    setInsertPosition(null);
  }

  const reg = QuestionTypeRegistry[question.type as keyof typeof QuestionTypeRegistry];

  drag(drop(ref));

  return (
    <div className="relative">
      {/* 顶部插入线 */}
      {insertPosition === 'top' && (
        <div className="absolute -top-1 left-0 right-0 h-0.5 bg-blue-500 dark:bg-blue-400 rounded-full z-10 shadow-lg animate-pulse" />
      )}
      
      <div
        ref={ref}
        style={{ opacity: isDragging ? 0.5 : 1 }}
        className={`group rounded-xl border border-border bg-card/90 shadow flex items-center px-4 py-3 gap-4 transition cursor-pointer ${
          isSelected ? "ring-2 ring-blue-500" : "hover:ring-1 hover:ring-border"
        } ${isOver ? 'bg-accent/20' : ''}`}
        onClick={onSelect}
      >
        <span className="text-muted-foreground font-mono w-6 text-center">{index + 1}</span>
        <div className="flex-1">

          {
            question.type !== QuestionType.Text && (
              <div className="mb-2 font-medium text-card-foreground flex items-center gap-1">
              {question.props?.required && (
                <span className="text-red-500 text-lg">*</span>
              )}
                  <span>{question.props?.title || "未命名题目"}</span>
            </div>
            )
          }
         
          <div className="text-sm text-muted-foreground">
            {reg?.render && reg.render({ props: question.props as any })}
          </div>
        </div>
        {isSelected && (
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground hover:text-red-500"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <span className="text-lg">✕</span>
            </Button>
            <div className="drag-handle cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground p-1">
              <GripVertical className="h-5 w-5" />
            </div>
          </div>
        )}
      </div>

      {/* 底部插入线 */}
      {insertPosition === 'bottom' && (
        <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-blue-500 dark:bg-blue-400 rounded-full z-10 shadow-lg animate-pulse" />
      )}
    </div>
  );
} 