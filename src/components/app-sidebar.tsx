"use client"

import * as React from "react"
import { FileText, User2 } from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useUserStore } from "@/store/user"
import SmartSurveyLogo from "@/components/SmartSurveyLogo"
import { useSidebar } from "@/components/ui/sidebar"

// This is sample data.
export const data = {
  navMain: [
    {
      title: "问卷",
      url: "/questionnaire",
      icon: FileText,
      isActive: true,
      // items: [],
    },
    {
      title: "用户",
      url: "/user",
      icon: User2,
      isActive: false,
      // items: [],
    },
  ]
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useUserStore();
  const { open } = useSidebar();

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-2 py-4">
          <SmartSurveyLogo />
          {
            open && (
              <span className="font-bold text-lg tracking-tight text-sidebar-foreground">智能问卷</span>
            )
          }
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
