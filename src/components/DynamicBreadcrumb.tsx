"use client";

import { usePathname } from "next/navigation";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { data } from "@/components/app-sidebar"; // 假设你导出了菜单配置

function DynamicBreadcrumb() {
  const pathname = usePathname();

  // 只处理一级菜单
  const current = data.navMain.find(item => pathname.startsWith(item.url));
  const crumbs = [
    { title: "首页", url: "/" },
    ...(current ? [current] : []),
  ];

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {crumbs.map((item, idx) => (
          <BreadcrumbItem key={item.url}>
            {idx < crumbs.length - 1 ? (
              <>
                <BreadcrumbLink href={item.url}>{item.title}</BreadcrumbLink>
                <BreadcrumbSeparator />
              </>
            ) : (
              <BreadcrumbPage>{item.title}</BreadcrumbPage>
            )}
          </BreadcrumbItem>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

export default DynamicBreadcrumb;
