/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { PageRequest } from "@/types/common"

// interface TableParams extends PageRequest {
 
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   [key: string]: any;
// }

// interface UseTableParamsProps {
//   defaultParams?: Partial<TableParams>;
// }



export function useTableParams<TableParams extends PageRequest>(options: {
  defaultParams: TableParams;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // 初始化状态
  const [params, setParamsState] = useState<TableParams>(() => {
    const urlParams = Object.fromEntries(searchParams.entries());
    return {
      ...options.defaultParams,
      ...urlParams,
      page: Number(searchParams.get("page")) || options.defaultParams.page || 0,
      size: Number(searchParams.get("size")) || options.defaultParams.size || 10,
    };
  });

  // 更新 URL 和状态
  const setParams = useCallback(
    (newParams: Partial<TableParams>) => {
      const updatedParams = {
        ...params,
        ...newParams,
      };

      // 更新状态
      setParamsState(updatedParams);

      // 构建新的 URL 参数
      const urlParams = new URLSearchParams();
      Object.entries(updatedParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          urlParams.set(key, String(value));
        }
      });

      // 更新 URL，但不重新加载页面
      router.replace(`${pathname}?${urlParams.toString()}`, { scroll: false });
    },
    [params, pathname, router]
  );

  // 监听 URL 参数变化
  useEffect(() => {
    const urlParams = Object.fromEntries(searchParams.entries());
    const newParams = {
      ...options.defaultParams,
      ...urlParams,
      page: Number(searchParams.get("page")) || options.defaultParams.page || 0,
      size: Number(searchParams.get("size")) || options.defaultParams.size || 10,
    };

    setParamsState(newParams as TableParams);
  }, [searchParams, options.defaultParams]);

  return {
    params,
    setParams,
    // 便捷方法
    setPage: (page: number) => setParams({ page } as Partial<TableParams>),
    setPageSize: (size: number) => setParams({ page: 0, size } as Partial<TableParams>),
  };
} 
