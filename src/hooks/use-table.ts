import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
} from "@tanstack/react-table";

import { useTableParams } from "./use-table-params";
import { useQuery } from "@tanstack/react-query";
import { PageResult, PageRequest } from "@/types/common";
// import { useDebounceFn } from "./use-debounce-fn";

interface DataTableOptions<TData, TParams extends PageRequest> {
  columns: ColumnDef<TData, any>[];
  queryKey: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  defaultParams?: TParams;
}

export function useTable<TData, TParams extends PageRequest>(
  apiService:(
    params: TParams
  ) => Promise<PageResult<TData>>,
  options: DataTableOptions<TData,TParams>
) {
  const { columns, queryKey, defaultParams } = options;
  const { params, setParams } = useTableParams<TParams>({
    defaultParams: defaultParams ?? ({} as TParams)
  });

  const { data, isLoading } = useQuery({
    queryKey: [queryKey, JSON.stringify(params)],
    queryFn: async () => {
      try {
        const data  = await apiService(params)
        return data;

      } catch(e) {
      }
      return {
        list: [],
        pages: 0,
        page: params.page,
        size: params.size
      }
    },
  });

  const table = useReactTable({
    data: data?.list ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    pageCount: data?.pages ?? 0,
    state: {
      pagination: {
        pageIndex: Number(params.page),
        pageSize: Number(params.size),
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: Number(params.page),
          pageSize: Number(params.size),
        });
        setParams({
          page: newPagination.pageIndex,
          size: newPagination.pageSize,
        } as Partial<TParams>);
      }
    },
    manualPagination: true,
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // const { run: setPageParams } = useDebounceFn((currentParams: any = {}) => {
  //   setParams({
  //     ...params,
  //     pageIndex: 0,
  //     pageSize: 10,
  //     ...currentParams
  //   })
  // }, 500);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const setPageParams = (currentParams: any = {}) => {
    setParams({
      ...params,
      page: 0,
      ...currentParams,
    });
  };

  return {
    data: data,
    setParams: setPageParams,
    params,
    isLoading,
    table,
  };
}
