import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
} from "@tanstack/react-table";

import { useTableParams } from "./use-table-params";
import { useQuery } from "@tanstack/react-query";
import { PageResult, PageRequest } from "@/types/common";
// import { useDebounceFn } from "./use-debounce-fn";

interface DataTableOptions<TData, TParams extends PageRequest> {
  columns: ColumnDef<TData, any>[];
  queryKey: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  defaultParams?: TParams;
}

export function useTable<TData, TParams extends PageRequest>(
  apiService: (params: TParams) => Promise<PageResult<TData>>,
  options: DataTableOptions<TData, TParams>
) {
  const { columns, queryKey, defaultParams } = options;
  const { params, setParams } = useTableParams<TParams>({
    defaultParams: defaultParams ?? ({} as TParams),
  });

  const { data, isLoading } = useQuery({
    queryKey: [queryKey, JSON.stringify(params)],
    refetchOnMount: true,
    queryFn: async () => {
      try {
        const data = await apiService({
          ...params,
          page: Number(params.page) + 1, // 前端 0-based 转换为后端 1-based
        });
        return data;
      } catch (e) {
        console.error('Table data fetch error:', e);
        return {
          list: [],
          pages: 0,
          page: Number(params.page) + 1, // 保持与 API 一致的页码
          size: Number(params.size),
          total: 0,
        };
      }
    },
  });

  const table = useReactTable({
    data: data?.list ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    pageCount: data?.pages ?? 0,
    state: {
      pagination: {
        pageIndex: Number(params.page), // 前端使用 0-based 索引
        pageSize: Number(params.size),
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const currentPagination = {
          pageIndex: Number(params.page),
          pageSize: Number(params.size),
        };
        const newPagination = updater(currentPagination);

        // 更新参数，保持 0-based 索引
        setParams({
          page: newPagination.pageIndex,
          size: newPagination.pageSize,
        } as Partial<TParams>);
      }
    },
    manualPagination: true,
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // const { run: setPageParams } = useDebounceFn((currentParams: any = {}) => {
  //   setParams({
  //     ...params,
  //     pageIndex: 0,
  //     pageSize: 10,
  //     ...currentParams
  //   })
  // }, 500);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const setPageParams = (currentParams: any = {}) => {
    setParams({
      ...params,
      page: 0,
      ...currentParams,
    });
  };

  return {
    data: data,
    setParams: setPageParams,
    params,
    isLoading,
    table,
  };
}
