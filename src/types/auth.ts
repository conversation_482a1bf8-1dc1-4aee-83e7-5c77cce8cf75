export interface AccessTokenDTO {
  token: string;
}



export interface RoleResponse {
  id: number;
  name: string;
  code: string;
  description?: string;
}

export interface UserInfo {
  id: number;
  username: string;
  realName?: string;
  phone?: string;
  avatar?: string;
  email?: string;
  status: number;
  remark?: string;
  lastLoginTime?: string;
  loginFailCount: number;
  loginLockTime?: string;
  passwordExpireDate?: string;
  createTime?: string;
  updateTime?: string;
  roles?: RoleResponse[];
} 