// 问卷类型
export enum QuestionnaireType {
  Questionnaire = 1, // 问卷
  Form = 2,         // 表单
}

// 问卷状态
export enum QuestionnaireStatus {
  Draft = 1,   // 草稿
  Published = 2, // 发布
  Disabled = 3,  // 停用
}

// 问题大类
export enum QuestionModule {
  Tip = 1,         // 提示文本
  Input = 2,       // 输入题
  Choice = 3,      // 选择题
  Date = 4,        // 日期题
  Area = 5,        // 区域题
  Special = 6,     // 特殊类型
}

// 问题类型
export enum QuestionType {
  Text = "text",                // 文本
  Input = "input",              // 单行输入
  Textarea = "textarea",        // 多行输入
  InputNumber = "inputNumber",  // 数字输入
  Radio = "radio",              // 单选
  Checkbox = "checkbox",        // 多选
  RadioInput = "radioInput",    // 单选输入
  CheckboxInput = "checkboxInput", // 多选输入
  Select = "select",            // 下拉选择
  Slider = "slider",            // 拖拽选择
  Province = "province",        // 省选择
  ProvinceCity = "provinceCity",// 省市
  Area = "area",                // 省市区
  Year = "year",                // 年
  Month = "month",              // 月
  Date = "date",                // 年月日
  DateTime = "dateTime",        // 年月日时分秒
  Time = "time",                // 时间
  SEX = "sex" //性别，特殊样式
}

export interface QuestionProps {
  title: string;
  code?: string;
  required: boolean;
  description?: string;
  guideType: number;
  guideFileUrl?: string;
  guideFileCoverUrl?: string;
  unit?: string;
  alerts?: Record<string, unknown>;
  conclusion?: Record<string, unknown>;
  labels?: Record<string, unknown>;
  [key: string]: any;
}

export interface QuestionnaireResponseDTO {
  id: string;
  title: string;
  type: QuestionnaireType;
  description?: string;
  status: QuestionnaireStatus;
  questionNum: number;
  totalScore: string; // BigDecimal 用 string 表示
  computeRatio: string; // BigDecimal 用 string 表示
  startTime?: string; // LocalDateTime 用 ISO 字符串
  endTime?: string;
  isAnonymous: boolean;
  allowMultiple: boolean;
  maxSubmissions?: number;
  questions: QuestionResponseDTO[];
  logicExpressions?: QuestionLogicExpression[]
  createUserId?: number;
  updateUserId?: number;
  createTime?: string;
  updateTime?: string;
}

export interface QuestionLogicExpression {
  id?: string;
  questionnaireId: string;
  targetQuestionId: string;
  expression: string;
  actionType: "SHOW" | "HIDE";
  priority?: number;
  crud: 1 | 2 | 3 | 4;
}

export interface QuestionResponseDTO {
  id?: string;
  type: QuestionType;
  module?: QuestionModule;
  sortOrder: number;
  props: AllQuestionProps;
  crud: 1 | 2 | 3 | 4;
}

export interface OptionItem {
  label: string;
  value: string;
  hasInput: boolean;
}

// 基础props
export interface BaseQuestionProps {
  title: string;
  code?: string;
  required: boolean;
}

// 文本题型扩展
export interface TextQuestionProps extends BaseQuestionProps {
  fontSize?: number;
  fontWeight?: number;
  color?: string;
}

// 单行输入题型扩展
export interface InputQuestionProps extends BaseQuestionProps {
  unit?: string;
  placeholder?: string;
}

// 多行输入题型扩展
export interface TextareaQuestionProps extends BaseQuestionProps {
  placeholder?: string;
}

// 数字输入题型扩展
export interface InputNumberQuestionProps extends BaseQuestionProps {
  unit?: string;
  placeholder?: string;
  min?: number;
  max?: number;
}

// 单选输入题型扩展
export interface RadioInputQuestionProps extends BaseQuestionProps {
  options: OptionItem[];
}

// 多选输入题型扩展
export interface CheckboxInputQuestionProps extends BaseQuestionProps {
  options: OptionItem[];
}

// 单选题型扩展
export interface RadioQuestionProps extends BaseQuestionProps {
  options: OptionItem[];
}

// 多选题型扩展
export interface CheckboxQuestionProps extends BaseQuestionProps {
  options: OptionItem[];
}

// 下拉选择题型扩展
export interface SelectQuestionProps extends BaseQuestionProps {
  options: OptionItem[];
}

// 拖拽选择题型扩展
export interface SliderQuestionProps extends BaseQuestionProps {
  min?: number;
  max?: number;
  defaultValue?: number;
}

// 日期类题型扩展
export interface DateQuestionProps extends BaseQuestionProps {
  min?: string;
  max?: string;
}

export interface DateTimeQuestionProps extends BaseQuestionProps {
  min?: string;
  max?: string;
}

export interface TimeQuestionProps extends BaseQuestionProps {
  min?: string;
  max?: string;
}

export interface YearQuestionProps extends BaseQuestionProps {
  min?: number;
  max?: number;
}

export interface MonthQuestionProps extends BaseQuestionProps {
  min?: string;
  max?: string;
}

// 区域类题型扩展
export interface ProvinceQuestionProps extends BaseQuestionProps {}
export interface ProvinceCityQuestionProps extends BaseQuestionProps {}
export interface AreaQuestionProps extends BaseQuestionProps {}

// 性别题型扩展
export interface SexQuestionProps extends BaseQuestionProps {}

// 联合类型
export type AllQuestionProps =
  | TextQuestionProps
  | InputQuestionProps
  | TextareaQuestionProps
  | InputNumberQuestionProps
  | RadioInputQuestionProps
  | CheckboxInputQuestionProps
  | RadioQuestionProps
  | CheckboxQuestionProps
  | SelectQuestionProps
  | SliderQuestionProps
  | DateQuestionProps
  | DateTimeQuestionProps
  | TimeQuestionProps
  | YearQuestionProps
  | MonthQuestionProps
  | ProvinceQuestionProps
  | ProvinceCityQuestionProps
  | AreaQuestionProps
  | SexQuestionProps;

// 属性描述类型，name只能是对应props类型的key
export type PropDescriptor<K extends string = string> = {
  name: K;
  label: string;
  type: string;
  disabled?: boolean;
  defaultValue?: any;
  // 其它可选字段
};

// 题型与props类型的映射
export type QuestionTypePropsMap = {
  [QuestionType.Text]: TextQuestionProps;
  [QuestionType.Input]: InputQuestionProps;
  [QuestionType.Textarea]: TextareaQuestionProps;
  [QuestionType.InputNumber]: InputNumberQuestionProps;
  [QuestionType.RadioInput]: RadioInputQuestionProps;
  [QuestionType.CheckboxInput]: CheckboxInputQuestionProps;
  [QuestionType.Radio]: RadioQuestionProps;
  [QuestionType.Checkbox]: CheckboxQuestionProps;
  [QuestionType.Select]: SelectQuestionProps;
  [QuestionType.Slider]: SliderQuestionProps;
  [QuestionType.Date]: DateQuestionProps;
  [QuestionType.DateTime]: DateTimeQuestionProps;
  [QuestionType.Time]: TimeQuestionProps;
  [QuestionType.Year]: YearQuestionProps;
  [QuestionType.Month]: MonthQuestionProps;
  [QuestionType.Province]: ProvinceQuestionProps;
  [QuestionType.ProvinceCity]: ProvinceCityQuestionProps;
  [QuestionType.Area]: AreaQuestionProps;
  [QuestionType.SEX]: SexQuestionProps;
};

export type RenderFnProps<T> = {
  props: T;
  value?: any;
  style?: React.CSSProperties;
  onChange?: (val: any) => void;
};

// 注册表类型，props为类型安全的name
export type QuestionTypeRegistryType = {
  [K in keyof QuestionTypePropsMap]: {
    key: K;
    moduleName: QuestionModule;
    label: string;
    icon: React.ReactNode;
    preview: () => string;
    render: (args: RenderFnProps<QuestionTypePropsMap[K]>) => React.ReactNode;
    props: Array<PropDescriptor<Extract<keyof QuestionTypePropsMap[K], string>>>;
  }
};