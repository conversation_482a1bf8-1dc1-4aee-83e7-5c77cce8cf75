# Next.js 问卷系统 - Docker 部署指南

## 🚀 快速开始

### 本地测试部署

```bash
# 构建并运行测试环境
./deploy.sh test

# 访问应用
open http://localhost:6603
```

### 生产环境部署

```bash
# 构建并推送生产镜像
./deploy.sh prod

# 本地测试生产镜像
./deploy.sh run-prod
open http://localhost:8080
```

## 📁 项目结构

```
├── .gitlab-ci.yml          # GitLab CI/CD 配置
├── Dockerfile              # Docker 构建文件
├── default.conf             # Nginx 配置
├── next.config.js           # Next.js 配置（支持静态导出）
├── deploy.sh                # 本地部署脚本
├── .env.test                # 测试环境变量
├── .env.production          # 生产环境变量
└── DEPLOYMENT.md            # 详细部署文档
```

## 🔧 配置说明

### 环境变量

#### 测试环境 (`.env.test`)
```env
NODE_ENV=test
NEXT_PUBLIC_API_BASE_URL=https://test-api.yourdomain.com
NEXT_PUBLIC_APP_ENV=test
```

#### 生产环境 (`.env.production`)
```env
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_ENV=production
```

### Docker 构建参数

- `BUILD_ENV=test`: 测试环境构建
- `BUILD_ENV=prod`: 生产环境构建

## 🏗️ CI/CD 流程

### GitLab CI/CD 阶段

1. **deploy**: 测试环境部署 (`dev` 分支)
2. **deploy-prod**: 生产环境部署 (`master` 分支)
3. **clean**: 清理 Docker 资源

### 触发条件

- **测试环境**: 推送到 `dev` 分支，手动触发
- **生产环境**: 推送到 `master` 分支，手动触发

## 🐳 Docker 配置

### 多阶段构建

1. **构建阶段**: 使用 Node.js 16.14.2 构建应用
2. **运行阶段**: 使用 Nginx 提供静态文件服务

### 端口映射

- **测试环境**: 6603:80
- **生产环境**: 根据部署环境配置

### 镜像仓库

- **私有仓库**: `192.168.0.249:5000/slan-bot/questionnaire-web`
- **版本标签**: `v1.0.0` (可在 CI 配置中修改)

## 📝 部署命令

### 本地部署脚本

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 测试环境部署
./deploy.sh test

# 生产环境构建
./deploy.sh prod

# 本地运行生产镜像
./deploy.sh run-prod

# 清理 Docker 资源
./deploy.sh cleanup
```

### 手动 Docker 命令

```bash
# 构建测试镜像
docker build --build-arg BUILD_ENV="test" -t questionnaire-web .

# 运行测试容器
docker run --name questionnaire-web -d -p 6603:80 questionnaire-web

# 构建生产镜像
docker build --build-arg BUILD_ENV="prod" -t questionnaire-web-prod .

# 标记并推送生产镜像
docker tag questionnaire-web-prod 192.168.0.249:5000/slan-bot/questionnaire-web:v1.0.0
docker push 192.168.0.249:5000/slan-bot/questionnaire-web:v1.0.0
```

## 🔍 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 检查 Node.js 版本
   docker run --rm node:16.14.2 node --version
   
   # 检查 pnpm 版本
   docker run --rm node:16.14.2 npm list -g pnpm
   ```

2. **容器启动失败**
   ```bash
   # 查看容器日志
   docker logs questionnaire-web
   
   # 进入容器调试
   docker exec -it questionnaire-web sh
   ```

3. **端口冲突**
   ```bash
   # 查看端口占用
   lsof -i :6603
   
   # 停止冲突容器
   docker stop $(docker ps -q --filter "publish=6603")
   ```

### 日志查看

```bash
# 查看容器日志
docker logs -f questionnaire-web

# 查看 Nginx 访问日志
docker exec questionnaire-web tail -f /var/log/nginx/access.log

# 查看 Nginx 错误日志
docker exec questionnaire-web tail -f /var/log/nginx/error.log
```

## 🔄 版本管理

### 更新版本

1. 修改 `.gitlab-ci.yml` 中的 `masterVersion`
2. 推送到 `master` 分支
3. 手动触发生产环境部署

### 回滚操作

```bash
# 拉取之前的版本
docker pull 192.168.0.249:5000/slan-bot/questionnaire-web:v1.0.0

# 停止当前容器
docker stop questionnaire-web-prod

# 运行之前的版本
docker run --name questionnaire-web-prod -d -p 80:80 \
  192.168.0.249:5000/slan-bot/questionnaire-web:v1.0.0
```

## 📞 支持

如有问题，请联系开发团队或查看详细的 [部署文档](./DEPLOYMENT.md)。
