version: '3.8'

services:
  questionnaire-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
    restart: unless-stopped
    networks:
      - questionnaire-network

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - questionnaire-app
    restart: unless-stopped
    networks:
      - questionnaire-network

networks:
  questionnaire-network:
    driver: bridge
