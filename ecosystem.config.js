module.exports = {
  apps: [
    {
      name: 'questionnaire-dev',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/questionnaire-dev',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 3001,
        NEXT_PUBLIC_API_BASE_URL: 'https://dev-api.yourdomain.com'
      }
    },
    {
      name: 'questionnaire-prod',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/questionnaire-prod',
      instances: 'max',
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NEXT_PUBLIC_API_BASE_URL: 'https://api.yourdomain.com'
      }
    }
  ]
};
