#!/bin/bash

# 部署脚本 - Next.js 问卷系统
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="questionnaire"
DEPLOY_USER="deploy"
BACKUP_DIR="/var/backups/$PROJECT_NAME"

# 函数：打印彩色消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 函数：备份当前版本
backup_current() {
    if [ -d "$1" ]; then
        print_message "备份当前版本..."
        sudo mkdir -p $BACKUP_DIR
        sudo cp -r $1 $BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)
        print_message "备份完成"
    fi
}

# 函数：部署到开发环境
deploy_development() {
    print_message "开始部署到开发环境..."
    
    DEPLOY_PATH="/var/www/$PROJECT_NAME-dev"
    
    # 备份
    backup_current $DEPLOY_PATH
    
    # 更新代码
    cd $DEPLOY_PATH
    git pull origin develop
    
    # 安装依赖
    pnpm install --frozen-lockfile
    
    # 构建
    pnpm build
    
    # 重启服务
    pm2 restart $PROJECT_NAME-dev || pm2 start ecosystem.config.js --env development
    
    print_message "开发环境部署完成！"
}

# 函数：部署到生产环境
deploy_production() {
    print_message "开始部署到生产环境..."
    
    DEPLOY_PATH="/var/www/$PROJECT_NAME-prod"
    
    # 备份
    backup_current $DEPLOY_PATH
    
    # 更新代码
    cd $DEPLOY_PATH
    git pull origin main
    
    # 安装依赖
    pnpm install --frozen-lockfile
    
    # 构建
    pnpm build
    
    # 重启服务
    pm2 restart $PROJECT_NAME-prod || pm2 start ecosystem.config.js --env production
    
    print_message "生产环境部署完成！"
}

# 函数：Docker 部署
deploy_docker() {
    print_message "开始 Docker 部署..."
    
    # 构建镜像
    docker build -t $PROJECT_NAME:latest .
    
    # 停止旧容器
    docker stop $PROJECT_NAME || true
    docker rm $PROJECT_NAME || true
    
    # 启动新容器
    docker run -d \
        --name $PROJECT_NAME \
        -p 3000:3000 \
        --restart unless-stopped \
        $PROJECT_NAME:latest
    
    print_message "Docker 部署完成！"
}

# 主函数
main() {
    print_message "Next.js 问卷系统部署脚本"
    
    # 检查必要命令
    check_command "git"
    check_command "pnpm"
    
    case "$1" in
        "dev"|"development")
            deploy_development
            ;;
        "prod"|"production")
            deploy_production
            ;;
        "docker")
            check_command "docker"
            deploy_docker
            ;;
        *)
            echo "使用方法: $0 {dev|prod|docker}"
            echo "  dev/development  - 部署到开发环境"
            echo "  prod/production  - 部署到生产环境"
            echo "  docker          - Docker 部署"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
