import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  basePath: '/web',
  assetPrefix: '/web',

  // 添加重定向配置
  async redirects() {
    return [
      {
        source: '/',
        destination: '/web',
        permanent: false,
      },
    ];
  },
  // 启用实验性功能
  experimental: {
    // 启用客户端组件优化
    optimizePackageImports: ['@tanstack/react-query', 'lucide-react'],
  },
  
  // 配置页面渲染模式
  compiler: {
    // 移除console.log（生产环境）
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  // 配置webpack以优化客户端渲染
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // 客户端优化
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
};

export default nextConfig;
