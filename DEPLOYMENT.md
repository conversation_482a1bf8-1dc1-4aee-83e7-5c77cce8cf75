# 部署文档 - Next.js 问卷系统

## GitLab CI/CD 配置

### 1. GitLab 变量配置

在 GitLab 项目的 **Settings > CI/CD > Variables** 中添加以下变量：

#### SSH 部署相关变量
```
SSH_PRIVATE_KEY          # SSH 私钥（用于连接服务器）
DEV_SERVER_HOST         # 开发服务器 IP 或域名
DEV_SERVER_USER         # 开发服务器用户名
PROD_SERVER_HOST        # 生产服务器 IP 或域名  
PROD_SERVER_USER        # 生产服务器用户名
```

#### Docker 部署相关变量（如果使用 Docker）
```
CI_REGISTRY_USER        # Docker 仓库用户名
CI_REGISTRY_PASSWORD    # Docker 仓库密码
CI_REGISTRY             # Docker 仓库地址
CI_REGISTRY_IMAGE       # Docker 镜像名称
```

### 2. 服务器准备

#### 开发服务器配置
```bash
# 创建部署目录
sudo mkdir -p /var/www/questionnaire-dev
sudo chown $USER:$USER /var/www/questionnaire-dev

# 克隆项目
cd /var/www/questionnaire-dev
git clone <your-repo-url> .
git checkout develop

# 安装依赖
npm install -g pnpm pm2
pnpm install
```

#### 生产服务器配置
```bash
# 创建部署目录
sudo mkdir -p /var/www/questionnaire-prod
sudo chown $USER:$USER /var/www/questionnaire-prod

# 克隆项目
cd /var/www/questionnaire-prod
git clone <your-repo-url> .
git checkout main

# 安装依赖
npm install -g pnpm pm2
pnpm install
```

### 3. 部署流程

#### 自动部署
- **开发环境**: 推送到 `develop` 分支自动触发部署
- **生产环境**: 推送到 `main` 分支后手动触发部署

#### 手动部署
```bash
# 使用部署脚本
chmod +x deploy.sh

# 部署到开发环境
./deploy.sh dev

# 部署到生产环境
./deploy.sh prod

# Docker 部署
./deploy.sh docker
```

### 4. Docker 部署

#### 构建和运行
```bash
# 构建镜像
docker build -t questionnaire:latest .

# 运行容器
docker run -d \
  --name questionnaire \
  -p 3000:3000 \
  --env-file .env.local \
  questionnaire:latest
```

#### 使用 Docker Compose
```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 5. Nginx 配置

#### 安装 Nginx
```bash
sudo apt update
sudo apt install nginx
```

#### 配置文件
将 `nginx.conf` 复制到 `/etc/nginx/sites-available/questionnaire`：

```bash
sudo cp nginx.conf /etc/nginx/sites-available/questionnaire
sudo ln -s /etc/nginx/sites-available/questionnaire /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. SSL 证书配置

#### 使用 Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d questionnaire.yourdomain.com
```

### 7. 监控和日志

#### PM2 监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs questionnaire-prod

# 监控面板
pm2 monit
```

#### 系统监控
```bash
# 查看系统资源
htop
df -h
free -h
```

### 8. 故障排除

#### 常见问题
1. **构建失败**: 检查 Node.js 版本和依赖
2. **部署失败**: 检查 SSH 密钥和服务器权限
3. **应用无法启动**: 检查环境变量和端口占用

#### 日志查看
```bash
# GitLab CI 日志
# 在 GitLab 项目的 CI/CD > Pipelines 中查看

# 应用日志
pm2 logs questionnaire-prod

# Nginx 日志
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

### 9. 回滚操作

#### 快速回滚
```bash
# 查看备份
ls -la /var/backups/questionnaire/

# 恢复备份
sudo cp -r /var/backups/questionnaire/backup-YYYYMMDD-HHMMSS/* /var/www/questionnaire-prod/
pm2 restart questionnaire-prod
```

#### Git 回滚
```bash
cd /var/www/questionnaire-prod
git log --oneline -10
git reset --hard <commit-hash>
pnpm install
pnpm build
pm2 restart questionnaire-prod
```
