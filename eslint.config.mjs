import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // 允许使用 any 类型
      "@typescript-eslint/no-explicit-any": "off",
      // 允许使用 any 类型作为函数参数
      "@typescript-eslint/no-unsafe-argument": "off",
      // 允许使用 any 类型的赋值
      "@typescript-eslint/no-unsafe-assignment": "off",
      // 允许调用 any 类型的方法
      "@typescript-eslint/no-unsafe-call": "off",
      // 允许访问 any 类型的成员
      "@typescript-eslint/no-unsafe-member-access": "off",
      // 允许返回 any 类型
      "@typescript-eslint/no-unsafe-return": "off",
      // 允许未使用的变量（可选，如果您也想放宽这个规则）
      "@typescript-eslint/no-unused-vars": "warn",
      // 允许空对象类型
      "@typescript-eslint/no-empty-object-type": "off",
      // 允许使用 wrapper object types
      "@typescript-eslint/no-wrapper-object-types": "off",
      // 允许使用 as const 断言
      "@typescript-eslint/prefer-as-const": "off",
      // 允许额外的非空断言
      "@typescript-eslint/no-extra-non-null-assertion": "off",
    },
  },
];

export default eslintConfig;
